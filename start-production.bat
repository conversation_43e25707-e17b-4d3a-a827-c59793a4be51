@echo off
REM Production startup script for VMA API with optimized JVM settings

echo Starting VMA API in production mode...

REM Set JVM arguments to handle Netty ByteBuf leaks and optimize performance
set JVM_ARGS=-Xms512m -Xmx2g
set JVM_ARGS=%JVM_ARGS% -XX:+UseG1GC
set JVM_ARGS=%JVM_ARGS% -XX:MaxGCPauseMillis=200
set JVM_ARGS=%JVM_ARGS% -XX:+UseStringDeduplication
set JVM_ARGS=%JVM_ARGS% -Dio.netty.leakDetection.level=DISABLED
set JVM_ARGS=%JVM_ARGS% -Dio.netty.allocator.type=pooled
set JVM_ARGS=%JVM_ARGS% -Dio.netty.allocator.numDirectArenas=0
set JVM_ARGS=%JVM_ARGS% -Dio.netty.noPreferDirect=true
set JVM_ARGS=%JVM_ARGS% -Dspring.profiles.active=prod

REM Database connection settings
set DB_URL=r2dbc:mssql://localhost:1433/vma_db
set DB_USERNAME=mssadmin
set DB_PASSWORD=pass

REM Server settings
set SERVER_PORT=8080

echo JVM Arguments: %JVM_ARGS%
echo Database URL: %DB_URL%
echo Server Port: %SERVER_PORT%

REM Build and run the application
call gradlew bootJar
if %ERRORLEVEL% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo Starting application...
java %JVM_ARGS% -jar build\libs\vma-api-0.0.1-SNAPSHOT.jar

pause
