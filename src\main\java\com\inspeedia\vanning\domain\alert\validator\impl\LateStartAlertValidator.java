package com.inspeedia.vanning.domain.alert.validator.impl;

import org.springframework.stereotype.Component;

import com.inspeedia.vanning.config.AlertProperties;
import com.inspeedia.vanning.domain.ActualWork;
import com.inspeedia.vanning.domain.PlannedWork;
import com.inspeedia.vanning.domain.alert.AlertType;
import com.inspeedia.vanning.domain.alert.validator.AbstractAlertValidator;

@Component
public class LateStartAlertValidator extends AbstractAlertValidator {
    private final AlertProperties alertProperties;

    public LateStartAlertValidator(AlertProperties alertProperties) {
        super(AlertType.LATE_START, 1);
        this.alertProperties = alertProperties;
    }

    @Override
    public boolean hasAlert(PlannedWork plannedWork, ActualWork actualWork) {
        boolean isEnabled = alertProperties.getEnabled().getOrDefault(alertType.name(), false);
        if (!isEnabled) {
            return false;
        }
        int threshold = alertProperties.getThresholds().getOrDefault("late-start-minutes", 30);
        return actualWork != null && plannedWork != null
                && actualWork.getStartTime() != null && plannedWork.getStartTime() != null
                && actualWork.getStartTime().isAfter(plannedWork.getStartTime().plusMinutes(threshold));
    }

    @Override
    public String getAlertDescription() {
        return alertType.getDescription();
    }
}
