package com.inspeedia.vanning.domain.alert.validator;

import com.inspeedia.vanning.domain.ActualWork;
import com.inspeedia.vanning.domain.PlannedWork;
import com.inspeedia.vanning.domain.alert.AlertType;

public interface AlertValidator {
    boolean hasAlert(PlannedWork plannedWork, ActualWork actualWork);
    AlertType getAlertType();
    int getPriority(); // For ordering validators if needed
    String getAlertDescription();
}
