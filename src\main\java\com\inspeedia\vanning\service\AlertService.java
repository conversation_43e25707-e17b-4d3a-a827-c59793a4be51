package com.inspeedia.vanning.service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import com.inspeedia.vanning.domain.ActualWork;
import com.inspeedia.vanning.domain.PlannedWork;
import com.inspeedia.vanning.domain.alert.AlertType;
import com.inspeedia.vanning.domain.alert.validator.AlertValidator;
import com.inspeedia.vanning.domain.alert.validator.registry.AlertValidatorRegistry;


@Service
public class AlertService {
    private final AlertValidatorRegistry validatorRegistry;

    public AlertService(AlertValidatorRegistry validatorRegistry) {
        this.validatorRegistry = validatorRegistry;
    }

    public List<Integer> calculateAlerts(PlannedWork plannedWork, ActualWork actualWork) {
        if (actualWork == null || plannedWork == null) {
            return List.of();
        }

        return validatorRegistry.getEnabledValidators().stream()
                .filter(validator -> validator.hasAlert(plannedWork, actualWork))
                .map(validator -> validator.getAlertType().getCode())
                .collect(Collectors.toList());
    }

    public Map<AlertType, String> getDetailedAlerts(PlannedWork plannedWork, ActualWork actualWork) {
        if (actualWork == null || plannedWork == null) {
            return Map.of();
        }

        return validatorRegistry.getEnabledValidators().stream()
                .filter(validator -> validator.hasAlert(plannedWork, actualWork))
                .collect(Collectors.toMap(
                        AlertValidator::getAlertType,
                        validator -> validator.getAlertDescription()
                ));
    }
}
