package com.inspeedia.vanning.domain;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import static org.junit.jupiter.api.Assertions.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * Unit tests for PlannedWork entity
 */
class PlannedWorkTest {

    private PlannedWork plannedWork;
    private LocalDate date;
    private LocalTime loadTime;
    private LocalTime startTime;
    private LocalTime endTime;
    private LocalTime duration;

    @BeforeEach
    @SuppressWarnings("unused")
    void setUp() {
        date = LocalDate.of(2024, 1, 15);
        loadTime = LocalTime.of(7, 30);
        startTime = LocalTime.of(8, 0);
        endTime = LocalTime.of(17, 0);
        duration = LocalTime.of(9, 0);

        plannedWork = new PlannedWork();
        plannedWork.setWorkDate(date);
        plannedWork.setRowName("A1");
        plannedWork.setOperatorName("田中太郎");
        plannedWork.setVanGp("AB");
        plannedWork.setDeliveryPlatform("A");
        plannedWork.setCollectionPlatform("B");
        plannedWork.setLoadTime(loadTime);
        plannedWork.setSize("L1");
        plannedWork.setStartTime(startTime);
        plannedWork.setEndTime(endTime);
        plannedWork.setDuration(duration);
        plannedWork.setSheetId(1);
        plannedWork.setRowId(5);
    }

    @Test
    void testDefaultConstructor() {
        PlannedWork work = new PlannedWork();
        assertNotNull(work);
        assertNull(work.getWorkDate());
        assertNull(work.getVanGp());
        assertNull(work.getDeliveryPlatform());
        assertNull(work.getCollectionPlatform());
        assertNull(work.getLoadTime());
        assertNull(work.getSize());
        assertNull(work.getStartTime());
        assertNull(work.getEndTime());
        assertNull(work.getDuration());
    }

    @Test
    void testParameterizedConstructor() {
        PlannedWork work = new PlannedWork(date, "A1", "佐藤花子", "CD", "C", "D", loadTime, "M2", startTime, endTime, duration);

        assertEquals(date, work.getWorkDate());
        assertEquals("A1", work.getRowName());
        assertEquals("佐藤花子", work.getOperatorName());
        assertEquals("CD", work.getVanGp());
        assertEquals("C", work.getDeliveryPlatform());
        assertEquals("D", work.getCollectionPlatform());
        assertEquals(loadTime, work.getLoadTime());
        assertEquals("M2", work.getSize());
        assertEquals(startTime, work.getStartTime());
        assertEquals(endTime, work.getEndTime());
        assertEquals(duration, work.getDuration());
    }

    @Test
    void testFullConstructor() {
        LocalDateTime now = LocalDateTime.now();
        PlannedWork work = new PlannedWork(1L, now, now, 1L, "admin", "admin", false,
                date, "A1", "山田一郎", "EF", "E", "F", loadTime, "S3", startTime, endTime, duration, 1, 5);

        assertEquals(1L, work.getId());
        assertEquals(now, work.getCreatedAt());
        assertEquals(now, work.getUpdatedAt());
        assertEquals(1L, work.getVersion());
        assertEquals("admin", work.getCreatedBy());
        assertEquals("admin", work.getUpdatedBy());
        assertFalse(work.isDeleted());
        assertEquals(date, work.getWorkDate());
        assertEquals("A1", work.getRowName());
        assertEquals("山田一郎", work.getOperatorName());
        assertEquals("EF", work.getVanGp());
        assertEquals("E", work.getDeliveryPlatform());
        assertEquals("F", work.getCollectionPlatform());
        assertEquals(loadTime, work.getLoadTime());
        assertEquals("S3", work.getSize());
        assertEquals(startTime, work.getStartTime());
        assertEquals(endTime, work.getEndTime());
        assertEquals(duration, work.getDuration());
    }

    @Test
    void testGettersAndSetters() {
        assertEquals(date, plannedWork.getWorkDate());
        assertEquals("A1", plannedWork.getRowName());
        assertEquals("田中太郎", plannedWork.getOperatorName());
        assertEquals("AB", plannedWork.getVanGp());
        assertEquals("A", plannedWork.getDeliveryPlatform());
        assertEquals("B", plannedWork.getCollectionPlatform());
        assertEquals(loadTime, plannedWork.getLoadTime());
        assertEquals("L1", plannedWork.getSize());
        assertEquals(startTime, plannedWork.getStartTime());
        assertEquals(endTime, plannedWork.getEndTime());
        assertEquals(duration, plannedWork.getDuration());

        // Test setters
        LocalDate newDate = LocalDate.of(2024, 2, 20);
        plannedWork.setWorkDate(newDate);
        plannedWork.setVanGp("XY");
        plannedWork.setSize("XL");

        assertEquals(newDate, plannedWork.getWorkDate());
        assertEquals("XY", plannedWork.getVanGp());
        assertEquals("XL", plannedWork.getSize());
    }

    @Test
    void testEquals() {
        PlannedWork work1 = new PlannedWork(date, "A1", "田中太郎", "AB", "A", "B", loadTime, "L1", startTime, endTime, duration);
        PlannedWork work2 = new PlannedWork(date, "A1", "田中太郎", "AB", "A", "B", loadTime, "L1", startTime, endTime, duration);
        PlannedWork work3 = new PlannedWork(date, "A1", "田中太郎", "CD", "A", "B", loadTime, "L1", startTime, endTime, duration);

        assertEquals(work1, work2);
        assertNotEquals(work1, work3);
        assertNotEquals(work1, null);
        assertNotEquals(work1, "string");
    }

    @Test
    void testHashCode() {
        PlannedWork work1 = new PlannedWork(date, "A1", "田中太郎", "AB", "A", "B", loadTime, "L1", startTime, endTime, duration);
        PlannedWork work2 = new PlannedWork(date, "A1", "田中太郎", "AB", "A", "B", loadTime, "L1", startTime, endTime, duration);

        assertEquals(work1.hashCode(), work2.hashCode());
    }

    @Test
    void testToString() {
        String toString = plannedWork.toString();

        assertNotNull(toString);
        assertTrue(toString.contains("PlannedWork"));
        assertTrue(toString.contains("vanGp='AB'"));
        assertTrue(toString.contains("deliveryPlatform='A'"));
        assertTrue(toString.contains("collectionPlatform='B'"));
        assertTrue(toString.contains("size='L1'"));
    }

    @Test
    void testInheritanceFromBaseEntity() {
        assertTrue(plannedWork instanceof BaseEntity);

        LocalDateTime now = LocalDateTime.now();
        plannedWork.setId(1L);
        plannedWork.setCreatedAt(now);
        plannedWork.setUpdatedAt(now);
        plannedWork.setVersion(1L);
        plannedWork.setCreatedBy("admin");
        plannedWork.setUpdatedBy("admin");
        plannedWork.setDeleted(false);

        assertEquals(1L, plannedWork.getId());
        assertEquals(now, plannedWork.getCreatedAt());
        assertEquals(now, plannedWork.getUpdatedAt());
        assertEquals(1L, plannedWork.getVersion());
        assertEquals("admin", plannedWork.getCreatedBy());
        assertEquals("admin", plannedWork.getUpdatedBy());
        assertFalse(plannedWork.isDeleted());
    }

    @Test
    void testSheetIdAndRowId() {
        plannedWork.setSheetId(2);
        plannedWork.setRowId(10);

        assertEquals(2, plannedWork.getSheetId());
        assertEquals(10, plannedWork.getRowId());
    }

    @Test
    void testSheetIdAndRowIdNullValues() {
        plannedWork.setSheetId(null);
        plannedWork.setRowId(null);

        assertNull(plannedWork.getSheetId());
        assertNull(plannedWork.getRowId());
    }
}
