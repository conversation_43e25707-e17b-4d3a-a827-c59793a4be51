package com.inspeedia.vanning.config;

import java.util.HashMap;
import java.util.Map;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Alert configuration properties
 * <p>
 * This class maps the alert-specific configuration properties from
 * application.yml to Java objects.
 */
@Component
@ConfigurationProperties(prefix = "alerts")
public class AlertProperties {
    private Map<String, Boolean> enabled = new HashMap<>();
    private Map<String, Integer> thresholds = new HashMap<>();
    private Map<String, Float> rateThresholds = new HashMap<>();

    // Getters and setters
    public Map<String, Boolean> getEnabled() { return enabled; }
    public void setEnabled(Map<String, Boolean> enabled) { this.enabled = enabled; }
    
    public Map<String, Integer> getThresholds() { return thresholds; }
    public void setThresholds(Map<String, Integer> thresholds) { this.thresholds = thresholds; }
    
    public Map<String, Float> getRateThresholds() { return rateThresholds; }
    public void setRateThresholds(Map<String, Float> rateThresholds) { this.rateThresholds = rateThresholds; }
}
