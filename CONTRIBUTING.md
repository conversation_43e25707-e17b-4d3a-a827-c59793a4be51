# Contributing to VMA API

Thank you for your interest in contributing to the Vanning Management API (VMA API)! This document provides guidelines and standards for contributing to this project.

## Table of Contents

- [Getting Started](#getting-started)
- [Development Setup](#development-setup)
- [Git Workflow](#git-workflow)
- [Branch Naming Convention](#branch-naming-convention)
- [Commit Message Standards](#commit-message-standards)
- [Code Standards](#code-standards)
- [Testing Guidelines](#testing-guidelines)
- [Pull Request Process](#pull-request-process)
- [Issue Reporting](#issue-reporting)

## Getting Started

### Prerequisites

- Java 17 or higher
- Gradle 8.5+
- SQL Server (for database)
- Git

### Development Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd vma-api
   ```

2. **Set up the database**
   - Ensure SQL Server is running
   - Create database `vma_db`
   - Update connection details in `application.yml` if needed

3. **Build and test**
   ```bash
   ./gradlew build
   ./gradlew test
   ```

4. **Run the application**
   ```bash
   ./gradlew bootRun
   # or use the convenience script
   ./start-dev.bat
   ```

## Git Workflow

We follow the **Git Flow** branching model with the following main branches:

- `main` - Production-ready code
- `develop` - Integration branch for features
- `feature/*` - Feature development branches
- `hotfix/*` - Critical bug fixes for production
- `release/*` - Release preparation branches

### Workflow Steps

1. **Start from develop branch**
   ```bash
   git checkout develop
   git pull origin develop
   ```

2. **Create feature branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

3. **Make changes and commit**
   ```bash
   git add .
   git commit -m "feat: add new feature description"
   ```

4. **Push and create Pull Request**
   ```bash
   git push origin feature/your-feature-name
   ```

## Branch Naming Convention

Use the following prefixes for branch names:

### Feature Branches
- `feature/` - New features or enhancements
- Examples:
  - `feature/excel-import-validation`
  - `feature/user-authentication`
  - `feature/api-rate-limiting`

### Bug Fix Branches
- `bugfix/` - Bug fixes for develop branch
- `hotfix/` - Critical fixes for production
- Examples:
  - `bugfix/fix-excel-parsing-error`
  - `hotfix/security-vulnerability-patch`

### Other Branch Types
- `chore/` - Maintenance tasks, dependency updates
- `docs/` - Documentation updates
- `refactor/` - Code refactoring without feature changes
- `test/` - Adding or updating tests

### Branch Naming Rules
- Use lowercase letters and hyphens
- Be descriptive but concise
- Include issue number if applicable: `feature/123-add-user-roles`

## Commit Message Standards

We follow the **Conventional Commits** specification for consistent commit messages.

### Format
```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

### Types
- `feat` - New feature
- `fix` - Bug fix
- `docs` - Documentation changes
- `style` - Code style changes (formatting, missing semicolons, etc.)
- `refactor` - Code refactoring
- `test` - Adding or updating tests
- `chore` - Maintenance tasks, dependency updates
- `perf` - Performance improvements
- `ci` - CI/CD changes
- `build` - Build system changes

### Examples

**Feature commit:**
```
feat(excel): add validation for duplicate entries

- Add duplicate detection logic in ExcelImportService
- Implement error reporting for duplicate records
- Add unit tests for validation scenarios

Closes #123
```

**Bug fix commit:**
```
fix(logging): resolve file path issue in logback configuration

The LOG_PATH variable was causing FileNotFoundException during startup.
Fixed by using hardcoded path and ensuring directory creation.

Fixes #456
```

**Breaking change commit:**
```
feat(api)!: change user authentication endpoint structure

BREAKING CHANGE: The /auth endpoint now returns different response format.
Old format: { "token": "..." }
New format: { "accessToken": "...", "refreshToken": "...", "expiresIn": 3600 }

Closes #789
```

### Commit Message Rules
- Use present tense ("add feature" not "added feature")
- Use imperative mood ("move cursor to..." not "moves cursor to...")
- Limit first line to 72 characters
- Reference issues and pull requests when applicable
- Include breaking change notice when applicable

## Code Standards

### Java Code Style
- Follow Google Java Style Guide
- Use meaningful variable and method names
- Add JavaDoc for public methods and classes
- Keep methods small and focused (max 20-30 lines)
- Use proper exception handling

### Spring Boot Best Practices
- Use `@Service`, `@Repository`, `@Controller` annotations appropriately
- Implement proper validation using Bean Validation
- Use reactive programming patterns with WebFlux
- Follow REST API conventions

### Database
- Use meaningful table and column names
- Follow database naming conventions (snake_case)
- Add proper indexes for performance
- Use migrations for schema changes

## Testing Guidelines

### Test Coverage
- Maintain minimum 80% code coverage
- Write unit tests for all service methods
- Add integration tests for API endpoints
- Include edge case testing

### Test Structure
```java
@Test
@DisplayName("Should create actual work when valid data provided")
void shouldCreateActualWorkWhenValidDataProvided() {
    // Given
    ActualWorkRequest request = createValidRequest();
    
    // When
    ActualWorkResponse response = actualWorkService.create(request);
    
    // Then
    assertThat(response.getId()).isNotNull();
    assertThat(response.getStatus()).isEqualTo("CREATED");
}
```

### Running Tests
```bash
# Run all tests
./gradlew test

# Run specific test class
./gradlew test --tests "ActualWorkServiceTest"

# Run tests with coverage
./gradlew test jacocoTestReport
```

## Pull Request Process

### Before Creating PR
1. Ensure all tests pass locally
2. Update documentation if needed
3. Add/update tests for new functionality
4. Follow code style guidelines
5. Rebase your branch on latest develop

### PR Requirements
- **Title**: Use conventional commit format
- **Description**: Explain what and why
- **Testing**: Describe how to test the changes
- **Screenshots**: Include for UI changes
- **Breaking Changes**: Clearly document any breaking changes

### PR Template
```markdown
## Description
Brief description of changes

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Breaking change
- [ ] Documentation update

## Testing
- [ ] Unit tests pass
- [ ] Integration tests pass
- [ ] Manual testing completed

## Checklist
- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
- [ ] Tests added/updated
```

### Review Process
1. At least one approval required
2. All CI checks must pass
3. No merge conflicts
4. Branch up to date with target branch

## Issue Reporting

### Bug Reports
Include:
- Steps to reproduce
- Expected vs actual behavior
- Environment details (Java version, OS, etc.)
- Error logs/stack traces
- Screenshots if applicable

### Feature Requests
Include:
- Clear description of the feature
- Use case and business value
- Acceptance criteria
- Any design considerations

### Labels
- `bug` - Something isn't working
- `enhancement` - New feature or request
- `documentation` - Improvements or additions to docs
- `good first issue` - Good for newcomers
- `help wanted` - Extra attention is needed
- `priority:high` - High priority items

## Getting Help

- Check existing issues and documentation
- Ask questions in pull request comments
- Contact the development team for complex issues

## License

By contributing, you agree that your contributions will be licensed under the same license as the project.

---

Thank you for contributing to VMA API! 🚀
