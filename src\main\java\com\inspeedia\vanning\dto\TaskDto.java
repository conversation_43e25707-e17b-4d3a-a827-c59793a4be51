package com.inspeedia.vanning.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * Task DTO representing a combined view of planned and actual work
 *
 * This DTO combines data from PlannedWork and ActualWork entities to provide a
 * unified task view for the frontend dashboard.
 */
public class TaskDto {

    private Long id;
    private String rowName;
    private String name;
    private String shippingDate;

    @JsonProperty("vangp")
    private String vanGp;

    private String deliveryTime;
    private String plannedStart;
    private String plannedEnd;
    private String plannedDuration;
    private String actualStart;
    private String actualEnd;
    private String actualDuration;
    private Integer progress;
    private Float progressRate;
    private List<Integer> alerts;

    public TaskDto() {
    }

    public TaskDto(Long id, String rowName, String name, String shippingDate, String vanGp,
            String deliveryTime, String plannedStart, String plannedEnd, String plannedDuration,
            String actualStart, String actualEnd, String actualDuration, Integer progress,
            Float progressRate, List<Integer> alerts) {
        this.id = id;
        this.rowName = rowName;
        this.name = name;
        this.shippingDate = shippingDate;
        this.vanGp = vanGp;
        this.deliveryTime = deliveryTime;
        this.plannedStart = plannedStart;
        this.plannedEnd = plannedEnd;
        this.plannedDuration = plannedDuration;
        this.actualStart = actualStart;
        this.actualEnd = actualEnd;
        this.actualDuration = actualDuration;
        this.progress = progress;
        this.progressRate = progressRate;
        this.alerts = alerts;
    }

    // Getters
    public Long getId() {
        return id;
    }

    public String getRowName() {
        return rowName;
    }

    public String getName() {
        return name;
    }

    public String getShippingDate() {
        return shippingDate;
    }

    public String getVanGp() {
        return vanGp;
    }

    public String getDeliveryTime() {
        return deliveryTime;
    }

    public String getPlannedStart() {
        return plannedStart;
    }

    public String getPlannedEnd() {
        return plannedEnd;
    }

    public String getPlannedDuration() {
        return plannedDuration;
    }

    public String getActualStart() {
        return actualStart;
    }

    public String getActualEnd() {
        return actualEnd;
    }

    public String getActualDuration() {
        return actualDuration;
    }

    public Integer getProgress() {
        return progress;
    }

    public Float getProgressRate() {
        return progressRate;
    }

    public List<Integer> getAlerts() {
        return alerts;
    }

    // Setters
    public void setId(Long id) {
        this.id = id;
    }

    public void setRowName(String rowName) {
        this.rowName = rowName;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setShippingDate(String shippingDate) {
        this.shippingDate = shippingDate;
    }

    public void setVanGp(String vanGp) {
        this.vanGp = vanGp;
    }

    public void setDeliveryTime(String deliveryTime) {
        this.deliveryTime = deliveryTime;
    }

    public void setPlannedStart(String plannedStart) {
        this.plannedStart = plannedStart;
    }

    public void setPlannedEnd(String plannedEnd) {
        this.plannedEnd = plannedEnd;
    }

    public void setPlannedDuration(String plannedDuration) {
        this.plannedDuration = plannedDuration;
    }

    public void setActualStart(String actualStart) {
        this.actualStart = actualStart;
    }

    public void setActualEnd(String actualEnd) {
        this.actualEnd = actualEnd;
    }

    public void setActualDuration(String actualDuration) {
        this.actualDuration = actualDuration;
    }

    public void setProgress(Integer progress) {
        this.progress = progress;
    }

    public void setProgressRate(Float progressRate) {
        this.progressRate = progressRate;
    }

    public void setAlerts(List<Integer> alerts) {
        this.alerts = alerts;
    }

    @Override
    public String toString() {
        return "TaskDto{"
                + "id=" + id
                + ", rowName=" + rowName + '\''
                + ", name='" + name + '\''
                + ", shippingDate='" + shippingDate + '\''
                + ", vanGp='" + vanGp + '\''
                + ", deliveryTime='" + deliveryTime + '\''
                + ", plannedStart='" + plannedStart + '\''
                + ", plannedEnd='" + plannedEnd + '\''
                + ", plannedDuration='" + plannedDuration + '\''
                + ", actualStart='" + actualStart + '\''
                + ", actualEnd='" + actualEnd + '\''
                + ", actualDuration='" + actualDuration + '\''
                + ", progress='" + progress + '\''
                + ", progressRate='" + progressRate + '\''
                + ", alerts=" + String.valueOf(alerts)
                + '}';
    }
}
