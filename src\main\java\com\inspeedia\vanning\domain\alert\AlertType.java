package com.inspeedia.vanning.domain.alert;

public enum AlertType {
    LATE_START(1, "Late Start", "Actual start is more than 30 minutes after planned start"),
    LOW_PROGRESS(2, "Low Progress", "Progress rate is below 40% and actual start is more than 40 minutes after planned start"),
    OVER_TIME(3, "Over Time", "Exceeded planned duration by significant margin");

    private final int code;
    private final String name;
    private final String description;

    AlertType(int code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }

    // Getters
    public int getCode() { return code; }
    public String getName() { return name; }
    public String getDescription() { return description; }
}
