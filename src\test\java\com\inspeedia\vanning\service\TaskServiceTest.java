package com.inspeedia.vanning.service;

import com.inspeedia.vanning.domain.ActualWork;
import com.inspeedia.vanning.domain.PlannedWork;
import com.inspeedia.vanning.dto.TaskDto;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.test.StepVerifier;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

/**
 * Unit tests for TaskService
 */
@ExtendWith(MockitoExtension.class)
class TaskServiceTest {

    @Mock
    private PlannedWorkService plannedWorkService;

    @Mock
    private ActualWorkService actualWorkService;

    @Mock
    private TaskConverter taskConverter;

    @Mock
    private RequestDeduplicationService requestDeduplicationService;

    @InjectMocks
    private TaskService taskService;

    private PlannedWork samplePlannedWork;
    private ActualWork sampleActualWork;
    private TaskDto sampleTaskDto;
    private String operatorName;
    private LocalDate today;

    @BeforeEach
    void setUp() {
        operatorName = "TestOperator";
        today = LocalDate.now();
        samplePlannedWork = new PlannedWork(
                LocalDate.of(2024, 1, 15),
                "A1",
                "User_AB_20240115",
                "AB",
                "C",
                "D",
                LocalTime.of(7, 30),
                "L1",
                LocalTime.of(8, 0),
                LocalTime.of(17, 0),
                LocalTime.of(9, 0)
        );
        samplePlannedWork.setId(1L);

        sampleActualWork = new ActualWork(
                "User_AB_20240115",
                LocalDate.of(2024, 1, 15),
                LocalTime.of(8, 15),
                LocalTime.of(17, 30),
                LocalTime.of(9, 15),
                85,
                85.0f,
                "AB",
                false
        );
        sampleActualWork.setId(1L);

        sampleTaskDto = new TaskDto();
        sampleTaskDto.setId(1L);
        sampleTaskDto.setRowName("A1");
        sampleTaskDto.setName("User_AB_20240115");
        sampleTaskDto.setShippingDate("2024-01-15");
        sampleTaskDto.setVanGp("AB");
        sampleTaskDto.setDeliveryTime("07:30");
        sampleTaskDto.setPlannedStart("08:00");
        sampleTaskDto.setPlannedEnd("17:00");
        sampleTaskDto.setPlannedDuration("9h");
        sampleTaskDto.setActualStart("08:15");
        sampleTaskDto.setActualEnd("17:30");
        sampleTaskDto.setActualDuration("9h 15m");
        sampleTaskDto.setProgress(85);
        sampleTaskDto.setProgressRate(85.0f);
    }

    private void setupDeduplicationMock() {
        // Mock the deduplication service to simply pass through the supplier
        when(requestDeduplicationService.deduplicateFlux(any(), any()))
                .thenAnswer(invocation -> {
                    java.util.function.Supplier<Flux<?>> supplier = invocation.getArgument(1);
                    return supplier.get();
                });
    }

    @Test
    void testGetAllTasks() {
        when(plannedWorkService.getAllActivePlannedWork(anyInt(), anyInt()))
                .thenReturn(Flux.just(samplePlannedWork));
        when(actualWorkService.getAllActiveActualWork(anyInt(), anyInt()))
                .thenReturn(Flux.just(sampleActualWork));
        when(taskConverter.convertToTaskDto(samplePlannedWork, sampleActualWork))
                .thenReturn(sampleTaskDto);

        StepVerifier.create(taskService.getAllTasks())
                .expectNextMatches(task -> {
                    return task.getId().equals(1L)
                            && task.getRowName().equals("A1")
                            && task.getName().equals("User_AB_20240115")
                            && task.getShippingDate().equals("2024-01-15")
                            && task.getVanGp().equals("AB")
                            && task.getDeliveryTime().equals("07:30")
                            && task.getPlannedStart().equals("08:00")
                            && task.getPlannedEnd().equals("17:00")
                            && task.getPlannedDuration().equals("9h")
                            && task.getActualStart().equals("08:15")
                            && task.getActualEnd().equals("17:30")
                            && task.getActualDuration().equals("9h 15m")
                            && task.getProgress().equals(85);
                })
                .verifyComplete();
    }

    @Test
    void testGetTodaysTasks() {
        when(plannedWorkService.getPlannedWorkForToday())
                .thenReturn(Flux.just(samplePlannedWork));
        when(actualWorkService.getActualWorkForCurrentDate())
                .thenReturn(Flux.just(sampleActualWork));
        when(taskConverter.convertToTaskDto(samplePlannedWork, sampleActualWork))
                .thenReturn(sampleTaskDto);

        StepVerifier.create(taskService.getTodayTasks())
                .expectNextMatches(task -> {
                    return task.getId().equals(1L)
                            && task.getVanGp().equals("AB")
                            && task.getProgress().equals(85);
                })
                .verifyComplete();
    }

    @Test
    void testGetAllTasksWithoutActualWork() {
        TaskDto emptyActualWorkDto = new TaskDto();
        emptyActualWorkDto.setId(1L);
        emptyActualWorkDto.setActualStart("");
        emptyActualWorkDto.setActualEnd("");
        emptyActualWorkDto.setActualDuration("");
        emptyActualWorkDto.setProgress(0);
        emptyActualWorkDto.setProgressRate(null);

        when(plannedWorkService.getAllActivePlannedWork(anyInt(), anyInt()))
                .thenReturn(Flux.just(samplePlannedWork));
        when(actualWorkService.getAllActiveActualWork(anyInt(), anyInt()))
                .thenReturn(Flux.empty());
        when(taskConverter.convertToTaskDto(samplePlannedWork, null))
                .thenReturn(emptyActualWorkDto);

        StepVerifier.create(taskService.getAllTasks())
                .expectNext(emptyActualWorkDto)
                .verifyComplete();
    }

    @Test
    void testGetAllTasksEmpty() {
        when(plannedWorkService.getAllActivePlannedWork(anyInt(), anyInt()))
                .thenReturn(Flux.empty());
        when(actualWorkService.getAllActiveActualWork(anyInt(), anyInt()))
                .thenReturn(Flux.empty());

        StepVerifier.create(taskService.getAllTasks())
                .verifyComplete();
    }

    @Test
    void testGetTodayTasksForOperator_PreservesExcelOrder() {
        setupDeduplicationMock();

        // Create planned work with different sheet_id and row_id values
        // The repository should return them in Excel order (sheet_id, row_id)
        PlannedWork planned1 = createPlannedWork(2L, 1, 3, "CD"); // Sheet 1, Row 3 (first)
        PlannedWork planned2 = createPlannedWork(4L, 1, 8, "GH"); // Sheet 1, Row 8 (second)
        PlannedWork planned3 = createPlannedWork(3L, 2, 2, "EF"); // Sheet 2, Row 2 (third)
        PlannedWork planned4 = createPlannedWork(1L, 2, 5, "AB"); // Sheet 2, Row 5 (fourth)

        // Mock the planned work service to return data in Excel order (as repository would)
        when(plannedWorkService.getPlannedWorkForOperatorFromToday(operatorName))
                .thenReturn(Flux.fromIterable(List.of(planned1, planned2, planned3, planned4)));

        // Mock actual work service to return empty (no actual work)
        when(actualWorkService.getActualWorkByUserNameAndWorkDate(eq(operatorName), eq(today)))
                .thenReturn(Flux.empty());

        // Mock task converter to return tasks with identifiable names
        when(taskConverter.convertToTaskDto(eq(planned1), any())).thenReturn(createTaskDto(2L, "CD"));
        when(taskConverter.convertToTaskDto(eq(planned2), any())).thenReturn(createTaskDto(4L, "GH"));
        when(taskConverter.convertToTaskDto(eq(planned3), any())).thenReturn(createTaskDto(3L, "EF"));
        when(taskConverter.convertToTaskDto(eq(planned4), any())).thenReturn(createTaskDto(1L, "AB"));

        // Execute the method
        Flux<TaskDto> result = taskService.getTodayTasksForOperator(operatorName);

        // Verify that results maintain Excel order
        StepVerifier.create(result)
                .expectNextMatches(task -> task.getName().equals("CD")) // Sheet 1, Row 3
                .expectNextMatches(task -> task.getName().equals("GH")) // Sheet 1, Row 8
                .expectNextMatches(task -> task.getName().equals("EF")) // Sheet 2, Row 2
                .expectNextMatches(task -> task.getName().equals("AB")) // Sheet 2, Row 5
                .verifyComplete();
    }

    @Test
    void testGetTodayTasksForOperator_HandlesNullSheetAndRowIds() {
        setupDeduplicationMock();

        // Create planned work with null sheet_id and row_id (legacy data)
        PlannedWork planned1 = createPlannedWork(1L, null, null, "AB");
        PlannedWork planned2 = createPlannedWork(2L, 1, 2, "CD");

        when(plannedWorkService.getPlannedWorkForOperatorFromToday(operatorName))
                .thenReturn(Flux.fromIterable(List.of(planned1, planned2)));

        when(actualWorkService.getActualWorkByUserNameAndWorkDate(eq(operatorName), eq(today)))
                .thenReturn(Flux.empty());

        when(taskConverter.convertToTaskDto(eq(planned1), any())).thenReturn(createTaskDto(1L, "AB"));
        when(taskConverter.convertToTaskDto(eq(planned2), any())).thenReturn(createTaskDto(2L, "CD"));

        Flux<TaskDto> result = taskService.getTodayTasksForOperator(operatorName);

        // Should handle null values gracefully and not break the pipeline
        StepVerifier.create(result)
                .expectNextCount(2)
                .verifyComplete();
    }

    @Test
    void testGetTodayTasksForOperator_EmptyOperatorName() {
        // Test with empty operator name - should return early without calling services
        Flux<TaskDto> result = taskService.getTodayTasksForOperator("");

        // Should return empty flux without breaking
        StepVerifier.create(result)
                .verifyComplete();
    }

    @Test
    void testGetTodayTasksForOperator_ActualWorkServiceError() {
        setupDeduplicationMock();

        // Create planned work
        PlannedWork plannedWork = createPlannedWork(1L, 1, 1, "AB");
        TaskDto expectedTask = createTaskDto(1L, "AB");

        // Mock planned work service to return data
        when(plannedWorkService.getPlannedWorkForOperatorFromToday(operatorName))
                .thenReturn(Flux.just(plannedWork));

        // Mock actual work service to throw error
        when(actualWorkService.getActualWorkByUserNameAndWorkDate(eq(operatorName), eq(today)))
                .thenReturn(Flux.error(new RuntimeException("Database connection error")));

        // Mock task converter to handle null actual work
        when(taskConverter.convertToTaskDto(eq(plannedWork), any()))
                .thenReturn(expectedTask);

        Flux<TaskDto> result = taskService.getTodayTasksForOperator(operatorName);

        // Should still return planned work even when actual work service fails
        StepVerifier.create(result)
                .expectNext(expectedTask)
                .verifyComplete();
    }

    @Test
    void testGetTodayTasksForOperator_TaskConverterError() {
        setupDeduplicationMock();

        // Create planned work and actual work with matching keys (same operatorName, workDate, vanGp)
        PlannedWork plannedWork = createPlannedWork(1L, 1, 1, "AB");
        ActualWork actualWork = new ActualWork(operatorName, today, LocalTime.of(8, 0), LocalTime.of(17, 0), LocalTime.of(9, 0), 8, null, "AB", false);
        TaskDto fallbackTask = createTaskDto(1L, "AB");

        // Mock services
        when(plannedWorkService.getPlannedWorkForOperatorFromToday(operatorName))
                .thenReturn(Flux.just(plannedWork));
        when(actualWorkService.getActualWorkByUserNameAndWorkDate(eq(operatorName), eq(today)))
                .thenReturn(Flux.just(actualWork));

        // Mock task converter to throw error with actual work, but succeed with null
        when(taskConverter.convertToTaskDto(plannedWork, actualWork))
                .thenThrow(new RuntimeException("Null pointer in progress rate"));
        when(taskConverter.convertToTaskDto(plannedWork, null))
                .thenReturn(fallbackTask);

        Flux<TaskDto> result = taskService.getTodayTasksForOperator(operatorName);

        // Should return task with planned work only when converter fails with actual work
        StepVerifier.create(result)
                .expectNext(fallbackTask)
                .verifyComplete();
    }

    private PlannedWork createPlannedWork(Long id, Integer sheetId, Integer rowId, String vanGp) {
        PlannedWork planned = new PlannedWork();
        planned.setId(id);
        planned.setRowName("A1");
        planned.setOperatorName(operatorName);
        planned.setWorkDate(today);
        planned.setVanGp(vanGp);
        planned.setDeliveryPlatform("A");
        planned.setCollectionPlatform("B");
        planned.setLoadTime(LocalTime.of(8, 0));
        planned.setSize("20");
        planned.setStartTime(LocalTime.of(9, 0));
        planned.setEndTime(LocalTime.of(17, 0));
        planned.setDuration(LocalTime.of(8, 0));
        planned.setSheetId(sheetId);
        planned.setRowId(rowId);
        return planned;
    }

    private TaskDto createTaskDto(Long id, String name) {
        TaskDto task = new TaskDto();
        task.setId(id);
        task.setName(name);
        task.setProgress(0);
        task.setProgressRate(0.0f);
        return task;
    }
}
