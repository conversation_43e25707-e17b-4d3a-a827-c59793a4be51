<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    
    <!-- Console Appender for tests -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%logger{36}] - %msg%n</pattern>
        </encoder>
    </appender>
    
    <!-- Root logger - only console output for tests -->
    <root level="WARN">
        <appender-ref ref="CONSOLE"/>
    </root>
    
    <!-- Application specific loggers -->
    <logger name="com.inspeedia.vanning" level="INFO" additivity="false">
        <appender-ref ref="CONSOLE"/>
    </logger>
    
    <!-- Reduce noise from Spring during tests -->
    <logger name="org.springframework" level="WARN"/>
    <logger name="org.springframework.boot" level="WARN"/>
    <logger name="org.springframework.test" level="WARN"/>
    <logger name="org.springframework.r2dbc" level="WARN"/>
    <logger name="org.springframework.security" level="WARN"/>
    <logger name="io.r2dbc" level="WARN"/>
    
</configuration>
