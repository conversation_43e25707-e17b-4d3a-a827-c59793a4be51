package com.inspeedia.vanning.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.context.event.EventListener;
import org.springframework.data.r2dbc.repository.config.EnableR2dbcRepositories;
import org.springframework.r2dbc.core.DatabaseClient;

import com.inspeedia.vanning.service.DatabaseMigrationService;

import io.r2dbc.spi.ConnectionFactory;

/**
 * Database configuration for R2DBC with MSSQL
 *
 * Provides reactive database connection setup and handles database migration
 * through DatabaseMigrationService with backup and rollback capabilities.
 */
@Configuration
@EnableR2dbcRepositories(basePackages = "com.inspeedia.vanning.repository")
public class DatabaseConfig {

    private static final Logger log = LoggerFactory.getLogger(DatabaseConfig.class);

    @Value("${spring.r2dbc.url}")
    private String databaseUrl;

    /**
     * Creates a DatabaseClient bean for custom database operations
     */
    @Bean
    public DatabaseClient databaseClient(ConnectionFactory connectionFactory) {
        log.info("Database client configured for: {}", databaseUrl);
        return DatabaseClient.create(connectionFactory);
    }

    /**
     * Handles database migration after application startup
     *
     * This runs schema creation and migration with proper error handling,
     * backup, and rollback capabilities.
     *
     * Note: Currently disabled due to circular dependency. Migration can be run
     * manually using DatabaseMigrationService.
     */
    // @EventListener(ApplicationReadyEvent.class)
    // @Profile("!test")
    public void handleDatabaseMigration() {
        log.info("Database migration is available but disabled to avoid circular dependency");
        log.info("Use DatabaseMigrationService directly for manual migration");

        // Manual migration example:
        // DatabaseMigrationService migrationService = new DatabaseMigrationService(
        //         databaseClient, true, "./backups", true, true, true);
        // migrationService.runMigrations().subscribe();
    }
}
