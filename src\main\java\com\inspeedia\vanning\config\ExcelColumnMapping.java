package com.inspeedia.vanning.config;

/**
 * Enum defining Excel column mappings for Japanese format import This
 * centralizes all column definitions to make maintenance easier
 */
public enum ExcelColumnMapping {

    // Column definitions: (columnIndex, headerName, fieldName, isRequired)
    // Note: Column indices are 0-based for Excel cell access
    ROW_NAME(0, "No", "rowName", true),
    DATE(1, "date", "date", true),
    VAN_GP(3, "VANGP", "vanGp", true),
    LOAD_TIME(4, "搬入時間", "loadTime", true),
    START_TIME(5, "開始時間", "startTime", true),
    END_TIME(6, "終了時間", "endTime", true),
    WORK_TIME(7, "作業時間", "duration", true);

    private final int columnIndex;
    private final String headerName;
    private final String fieldName;
    private final boolean required;

    ExcelColumnMapping(int columnIndex, String headerName, String fieldName, boolean required) {
        this.columnIndex = columnIndex;
        this.headerName = headerName;
        this.fieldName = fieldName;
        this.required = required;
    }

    public int getColumnIndex() {
        return columnIndex;
    }

    public String getHeaderName() {
        return headerName;
    }

    public String getFieldName() {
        return fieldName;
    }

    public boolean isRequired() {
        return required;
    }

    /**
     * Get all expected headers in order
     */
    public static String[] getExpectedHeaders() {
        ExcelColumnMapping[] mappings = values();
        String[] headers = new String[mappings.length];
        for (int i = 0; i < mappings.length; i++) {
            headers[i] = mappings[i].getHeaderName();
        }
        return headers;
    }

    /**
     * Find column mapping by header name
     */
    public static ExcelColumnMapping findByHeaderName(String headerName) {
        for (ExcelColumnMapping mapping : values()) {
            if (mapping.getHeaderName().equals(headerName)) {
                return mapping;
            }
        }
        return null;
    }

    /**
     * Find column mapping by column index
     */
    public static ExcelColumnMapping findByColumnIndex(int columnIndex) {
        for (ExcelColumnMapping mapping : values()) {
            if (mapping.getColumnIndex() == columnIndex) {
                return mapping;
            }
        }
        return null;
    }
}
