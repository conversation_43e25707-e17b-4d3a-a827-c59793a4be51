package com.inspeedia.vanning.service.excel;

import com.inspeedia.vanning.dto.RowDto;
import org.apache.poi.xssf.eventusermodel.XSSFSheetXMLHandler;
import org.apache.poi.xssf.usermodel.XSSFComment;
import reactor.core.publisher.FluxSink;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

class ReactiveSheetHandler implements XSSFSheetXMLHandler.SheetContentsHandler {

    private final String sheetName;
    private final int sheetId;
    private final FluxSink<RowDto> sink;
    private final List<String> currentRow = new ArrayList<>();
    private final AtomicInteger currentRowNum = new AtomicInteger(-1);

    ReactiveSheetHandler(String sheetName, int sheetId, FluxSink<RowDto> sink) {
        this.sheetName = sheetName;
        this.sheetId = sheetId;
        this.sink = sink;
    }

    @Override
    public void startRow(int rowNum) {
        currentRow.clear();
        currentRowNum.set(rowNum + 1);
    }

    @Override
    public void endRow(int rowNum) {
        sink.next(new RowDto(currentRowNum.get(), sheetName, sheetId, new ArrayList<>(currentRow)));
    }

    @Override
    public void cell(String cellReference, String formattedValue, XSSFComment comment) {
        currentRow.add(formattedValue == null ? "" : formattedValue);
    }

    @Override
    public void headerFooter(String text, boolean isHeader, String tagName) {
        // no-op
    }
}
