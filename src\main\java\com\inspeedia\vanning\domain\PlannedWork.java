package com.inspeedia.vanning.domain;

import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Objects;

/**
 * PlannedWork entity representing planned work in the VMA system
 *
 * This entity stores information about planned work including date, platform
 * details, timing, and size information.
 */
@Table("planned_work")
public class PlannedWork extends BaseEntity {

    @NotNull(message = "Work date is required")
    @Column("work_date")
    private LocalDate workDate;

    @NotBlank(message = "Row name is required")
    @Column("row_name")
    private String rowName;

    @NotBlank(message = "Operator name is required")
    @Size(max = 100, message = "Operator name must not exceed 100 characters")
    @Column("operator_name")
    private String operatorName;

    @NotBlank(message = "Van GP is required")
    @Size(min = 2, max = 2, message = "Van GP must be exactly 2 characters")
    @Pattern(regexp = "^[A-Z0-9]{2}$", message = "Van GP must be exactly 2 alphanumeric characters")
    @Column("van_gp")
    private String vanGp;

    @NotBlank(message = "Delivery platform is required")
    @Size(min = 1, max = 1, message = "Delivery platform must be exactly 1 character")
    @Pattern(regexp = "^[A-Z]$", message = "Delivery platform must be 1 uppercase alphabet")
    @Column("delivery_platform")
    private String deliveryPlatform;

    @NotBlank(message = "Collection platform is required")
    @Size(min = 1, max = 1, message = "Collection platform must be exactly 1 character")
    @Pattern(regexp = "^[A-Z]$", message = "Collection platform must be 1 uppercase alphabet")
    @Column("collection_platform")
    private String collectionPlatform;

    @NotNull(message = "Load time is required")
    @Column("load_time")
    private LocalTime loadTime;

    @NotBlank(message = "Size is required")
    @Size(min = 2, max = 2, message = "Size must be exactly 2 characters")
    @Pattern(regexp = "^[A-Z0-9]{2}$", message = "Size must be 2 alphanumeric characters")
    @Column("size")
    private String size;

    @NotNull(message = "Start time is required")
    @Column("start_time")
    private LocalTime startTime;

    @NotNull(message = "End time is required")
    @Column("end_time")
    private LocalTime endTime;

    @NotNull(message = "Duration is required")
    @Column("duration")
    private LocalTime duration;

    @Column("sheet_id")
    private Integer sheetId;

    @Column("row_id")
    private Integer rowId;

    public PlannedWork() {
        super();
    }

    public PlannedWork(LocalDate workDate, String rowName, String operatorName, String vanGp, String deliveryPlatform, String collectionPlatform,
            LocalTime loadTime, String size, LocalTime startTime, LocalTime endTime, LocalTime duration) {
        super();
        this.workDate = workDate;
        this.rowName = rowName;
        this.operatorName = operatorName;
        this.vanGp = vanGp;
        this.deliveryPlatform = deliveryPlatform;
        this.collectionPlatform = collectionPlatform;
        this.loadTime = loadTime;
        this.size = size;
        this.startTime = startTime;
        this.endTime = endTime;
        this.duration = duration;
        this.sheetId = null;
        this.rowId = null;
    }

    public PlannedWork(LocalDate workDate, String rowName, String operatorName, String vanGp, String deliveryPlatform, String collectionPlatform,
            LocalTime loadTime, String size, LocalTime startTime, LocalTime endTime, LocalTime duration,
            Integer sheetId, Integer rowId) {
        super();
        this.workDate = workDate;
        this.rowName = rowName;
        this.operatorName = operatorName;
        this.vanGp = vanGp;
        this.deliveryPlatform = deliveryPlatform;
        this.collectionPlatform = collectionPlatform;
        this.loadTime = loadTime;
        this.size = size;
        this.startTime = startTime;
        this.endTime = endTime;
        this.duration = duration;
        this.sheetId = sheetId;
        this.rowId = rowId;
    }

    public PlannedWork(Long id, LocalDateTime createdAt, LocalDateTime updatedAt, Long version,
            String createdBy, String updatedBy, boolean deleted,
            LocalDate workDate, String rowName, String operatorName, String vanGp, String deliveryPlatform, String collectionPlatform,
            LocalTime loadTime, String size, LocalTime startTime, LocalTime endTime, LocalTime duration,
            Integer sheetId, Integer rowId) {
        super(id, createdAt, updatedAt, version, createdBy, updatedBy, deleted);
        this.workDate = workDate;
        this.rowName = rowName;
        this.operatorName = operatorName;
        this.vanGp = vanGp;
        this.deliveryPlatform = deliveryPlatform;
        this.collectionPlatform = collectionPlatform;
        this.loadTime = loadTime;
        this.size = size;
        this.startTime = startTime;
        this.endTime = endTime;
        this.duration = duration;
        this.sheetId = sheetId;
        this.rowId = rowId;
    }

    // Getters
    public LocalDate getWorkDate() {
        return workDate;
    }

    public String getRowName() {
        return rowName;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public String getVanGp() {
        return vanGp;
    }

    public String getDeliveryPlatform() {
        return deliveryPlatform;
    }

    public String getCollectionPlatform() {
        return collectionPlatform;
    }

    public LocalTime getLoadTime() {
        return loadTime;
    }

    public String getSize() {
        return size;
    }

    public LocalTime getStartTime() {
        return startTime;
    }

    public LocalTime getEndTime() {
        return endTime;
    }

    public LocalTime getDuration() {
        return duration;
    }

    public Integer getSheetId() {
        return sheetId;
    }

    public Integer getRowId() {
        return rowId;
    }

    // Setters
    public void setWorkDate(LocalDate workDate) {
        this.workDate = workDate;
    }

    public void setRowName(String rowName) {
        this.rowName = rowName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public void setVanGp(String vanGp) {
        this.vanGp = vanGp;
    }

    public void setDeliveryPlatform(String deliveryPlatform) {
        this.deliveryPlatform = deliveryPlatform;
    }

    public void setCollectionPlatform(String collectionPlatform) {
        this.collectionPlatform = collectionPlatform;
    }

    public void setLoadTime(LocalTime loadTime) {
        this.loadTime = loadTime;
    }

    public void setSize(String size) {
        this.size = size;
    }

    public void setStartTime(LocalTime startTime) {
        this.startTime = startTime;
    }

    public void setEndTime(LocalTime endTime) {
        this.endTime = endTime;
    }

    public void setDuration(LocalTime duration) {
        this.duration = duration;
    }

    public void setSheetId(Integer sheetId) {
        this.sheetId = sheetId;
    }

    public void setRowId(Integer rowId) {
        this.rowId = rowId;
    }

    // equals method (calls super)
    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        PlannedWork that = (PlannedWork) o;
        return Objects.equals(workDate, that.workDate)
                && Objects.equals(rowName, that.rowName)
                && Objects.equals(operatorName, that.operatorName)
                && Objects.equals(vanGp, that.vanGp)
                && Objects.equals(deliveryPlatform, that.deliveryPlatform)
                && Objects.equals(collectionPlatform, that.collectionPlatform)
                && Objects.equals(loadTime, that.loadTime)
                && Objects.equals(size, that.size)
                && Objects.equals(startTime, that.startTime)
                && Objects.equals(endTime, that.endTime)
                && Objects.equals(duration, that.duration)
                && Objects.equals(sheetId, that.sheetId)
                && Objects.equals(rowId, that.rowId);
    }

    // hashCode method (calls super)
    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), workDate, rowName, operatorName, vanGp, deliveryPlatform, collectionPlatform,
                loadTime, size, startTime, endTime, duration, sheetId, rowId);
    }

    // toString method (calls super)
    @Override
    public String toString() {
        return "PlannedWork{"
                + "workDate=" + workDate
                + ", rowName='" + rowName + '\''
                + ", operatorName='" + operatorName + '\''
                + ", vanGp='" + vanGp + '\''
                + ", deliveryPlatform='" + deliveryPlatform + '\''
                + ", collectionPlatform='" + collectionPlatform + '\''
                + ", loadTime=" + loadTime
                + ", size='" + size + '\''
                + ", startTime=" + startTime
                + ", endTime=" + endTime
                + ", duration=" + duration
                + ", sheetId=" + sheetId
                + ", rowId=" + rowId
                + ", " + super.toString()
                + '}';
    }
}
