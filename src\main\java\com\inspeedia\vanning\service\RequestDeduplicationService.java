package com.inspeedia.vanning.service;

import java.time.Duration;
import java.util.concurrent.ConcurrentHashMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * Service to handle request deduplication for concurrent requests
 * 
 * This service prevents multiple identical requests from hitting the database
 * simultaneously by sharing the result of the first request with subsequent
 * identical requests within a short time window.
 */
@Service
public class RequestDeduplicationService {

    private static final Logger log = LoggerFactory.getLogger(RequestDeduplicationService.class);
    
    // Cache for ongoing requests to prevent duplicate database calls
    private final ConcurrentHashMap<String, Mono<?>> ongoingMonoRequests = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, Flux<?>> ongoingFluxRequests = new ConcurrentHashMap<>();
    
    // Duration to keep requests cached (500ms to handle rapid successive requests)
    private static final Duration DEDUPLICATION_DURATION = Duration.ofMillis(500);

    /**
     * Deduplicate Mono requests by sharing the result among concurrent identical requests
     * 
     * @param <T> the type of the result
     * @param requestKey unique key identifying the request
     * @param supplier the supplier that provides the Mono when not cached
     * @return Mono that either returns cached result or executes the supplier
     */
    @SuppressWarnings("unchecked")
    public <T> Mono<T> deduplicateMono(String requestKey, java.util.function.Supplier<Mono<T>> supplier) {
        log.debug("Deduplicating Mono request with key: {}", requestKey);
        
        return (Mono<T>) ongoingMonoRequests.computeIfAbsent(requestKey, key -> {
            log.debug("Creating new Mono for request key: {}", key);
            return supplier.get()
                    .cache(DEDUPLICATION_DURATION)
                    .doFinally(signalType -> {
                        log.debug("Removing Mono request from cache: {}, signal: {}", key, signalType);
                        ongoingMonoRequests.remove(key);
                    });
        });
    }

    /**
     * Deduplicate Flux requests by sharing the result among concurrent identical requests
     * 
     * @param <T> the type of the result
     * @param requestKey unique key identifying the request
     * @param supplier the supplier that provides the Flux when not cached
     * @return Flux that either returns cached result or executes the supplier
     */
    @SuppressWarnings("unchecked")
    public <T> Flux<T> deduplicateFlux(String requestKey, java.util.function.Supplier<Flux<T>> supplier) {
        log.debug("Deduplicating Flux request with key: {}", requestKey);
        
        return (Flux<T>) ongoingFluxRequests.computeIfAbsent(requestKey, key -> {
            log.debug("Creating new Flux for request key: {}", key);
            return supplier.get()
                    .cache(DEDUPLICATION_DURATION)
                    .doFinally(signalType -> {
                        log.debug("Removing Flux request from cache: {}, signal: {}", key, signalType);
                        ongoingFluxRequests.remove(key);
                    });
        });
    }

    /**
     * Clear all cached requests - useful for testing or when fresh data is required
     */
    public void clearCache() {
        log.debug("Clearing request deduplication cache");
        ongoingMonoRequests.clear();
        ongoingFluxRequests.clear();
    }

    /**
     * Get the number of ongoing Mono requests
     */
    public int getOngoingMonoRequestsCount() {
        return ongoingMonoRequests.size();
    }

    /**
     * Get the number of ongoing Flux requests
     */
    public int getOngoingFluxRequestsCount() {
        return ongoingFluxRequests.size();
    }
}
