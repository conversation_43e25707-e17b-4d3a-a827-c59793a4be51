-- VMA Database Schema
-- Creates tables for Vanning Management Application

-- Create actual_work table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='actual_work' AND xtype='U')
CREATE TABLE actual_work (
    id BIGINT IDENTITY(1,1) PRIMARY KEY,
    work_date DATE NOT NULL,
    operator_name NVARCHAR(100) NOT NULL,
    start_time TIME,
    end_time TIME,
    van_gp NVARCHAR(2) NOT NULL,
    duration TIME,
    progress INT CHECK (progress >= 0 AND progress <= 10),
    progress_rate FLOAT CHECK (progress_rate >= 0.0 AND progress_rate <= 100.0),
    created_at DATETIME2 DEFAULT GETDATE(),
    updated_at DATETIME2 DEFAULT GETDATE(),
    created_by NVARCHAR(50),
    updated_by NVARCHAR(50),
    version BIGINT DEFAULT 0,
    deleted BIT DEFAULT 0,
    completed BIT DEFAULT 0
);

-- Create planned_work table with sheet_id and row_id for Excel order preservation
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='planned_work' AND xtype='U')
CREATE TABLE planned_work (
    id BIGINT IDENTITY(1,1) PRIMARY KEY,
    work_date DATE NOT NULL,
    operator_name NVARCHAR(100) NOT NULL,
    van_gp NVARCHAR(2) NOT NULL,
    delivery_platform NVARCHAR(1) NOT NULL,
    collection_platform NVARCHAR(1) NOT NULL,
    load_time TIME NOT NULL,
    size NVARCHAR(2) NOT NULL CHECK (LEN(size) = 2),
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    duration TIME NOT NULL,
    sheet_id INT,
    row_id INT,
    row_name NVARCHAR(5),
    created_at DATETIME2 DEFAULT GETDATE(),
    updated_at DATETIME2 DEFAULT GETDATE(),
    created_by NVARCHAR(50),
    updated_by NVARCHAR(50),
    version BIGINT DEFAULT 0,
    deleted BIT DEFAULT 0,

    -- check constraints
    CONSTRAINT CHK_Van_GP CHECK (LEN(van_gp) = 2 AND van_gp = UPPER(van_gp)),
    CONSTRAINT CHK_Deliver_Platform CHECK (LEN(delivery_platform) = 1 AND delivery_platform = UPPER(delivery_platform)),
    CONSTRAINT CHK_Collection_Platform CHECK (LEN(collection_platform) = 1 AND collection_platform = UPPER(collection_platform))
);

-- Create indexes for performance
-- Actual work indexes
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_actual_work_operator_name')
CREATE INDEX IX_actual_work_operator_name ON actual_work(operator_name) WHERE deleted = 0;

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_actual_work_work_date')
CREATE INDEX IX_actual_work_work_date ON actual_work(work_date) WHERE deleted = 0;

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_actual_work_van_gp')
CREATE INDEX IX_actual_work_van_gp ON actual_work(van_gp) WHERE deleted = 0;

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_actual_work_progress')
CREATE INDEX IX_actual_work_progress ON actual_work(progress) WHERE deleted = 0;

-- Planned work indexes
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_planned_work_work_date')
CREATE INDEX IX_planned_work_work_date ON planned_work(work_date) WHERE deleted = 0;

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_planned_work_operator_name')
CREATE INDEX IX_planned_work_operator_name ON planned_work(operator_name) WHERE deleted = 0;

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_planned_work_van_gp')
CREATE INDEX IX_planned_work_van_gp ON planned_work(van_gp) WHERE deleted = 0;

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_planned_work_delivery_platform')
CREATE INDEX IX_planned_work_delivery_platform ON planned_work(delivery_platform) WHERE deleted = 0;

-- Excel order preservation index
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_planned_work_sheet_order')
CREATE INDEX IX_planned_work_sheet_order ON planned_work(work_date, sheet_id, row_id) WHERE deleted = 0;
