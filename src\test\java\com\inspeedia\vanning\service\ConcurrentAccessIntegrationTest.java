package com.inspeedia.vanning.service;

import com.inspeedia.vanning.domain.PlannedWork;
import com.inspeedia.vanning.dto.TaskDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.test.StepVerifier;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.List;
import java.util.ArrayList;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

/**
 * Integration tests for concurrent access scenarios
 *
 * These tests simulate the real-world scenario where multiple clients request
 * tasks within 500ms after Excel import completion.
 */
@ExtendWith(MockitoExtension.class)
class ConcurrentAccessIntegrationTest {

    @Mock
    private PlannedWorkService plannedWorkService;

    @Mock
    private ActualWorkService actualWorkService;

    @Mock
    private TaskConverter taskConverter;

    private RequestDeduplicationService requestDeduplicationService;
    private TaskService taskService;

    private PlannedWork samplePlannedWork;
    private TaskDto sampleTaskDto;
    private String operatorName;

    @BeforeEach
    void setUp() {
        requestDeduplicationService = new RequestDeduplicationService();
        taskService = new TaskService(plannedWorkService, actualWorkService, taskConverter, requestDeduplicationService);

        operatorName = "TestOperator";
        samplePlannedWork = new PlannedWork(
                LocalDate.now(),
                "A1",
                "User_AB_" + LocalDate.now().toString().replace("-", ""),
                "AB",
                "C",
                "D",
                LocalTime.of(7, 30),
                "L1",
                LocalTime.of(8, 0),
                LocalTime.of(17, 0),
                LocalTime.of(9, 0)
        );
        samplePlannedWork.setId(1L);

        sampleTaskDto = new TaskDto();
        sampleTaskDto.setId(1L);
        sampleTaskDto.setRowName("A1");
        sampleTaskDto.setName("User_AB_" + LocalDate.now().toString().replace("-", ""));
        sampleTaskDto.setShippingDate(LocalDate.now().toString());
        sampleTaskDto.setVanGp("AB");
        sampleTaskDto.setDeliveryTime("07:30");
        sampleTaskDto.setPlannedStart("08:00");
        sampleTaskDto.setPlannedEnd("17:00");
        sampleTaskDto.setPlannedDuration("9h");
    }

    @Test
    void testConcurrentRequestsWithin500ms_ShouldShareResult() throws InterruptedException {
        // Mock services to return data with a slight delay to simulate database access
        when(plannedWorkService.getPlannedWorkForOperatorFromToday(operatorName))
                .thenReturn(Flux.just(samplePlannedWork).delayElements(Duration.ofMillis(50)));
        when(actualWorkService.getActualWorkByUserNameAndWorkDate(anyString(), any()))
                .thenReturn(Flux.empty());
        when(taskConverter.convertToTaskDto(any(), any()))
                .thenReturn(sampleTaskDto);

        int numberOfConcurrentRequests = 5;
        CountDownLatch startLatch = new CountDownLatch(1);
        CountDownLatch completionLatch = new CountDownLatch(numberOfConcurrentRequests);
        ExecutorService executor = Executors.newFixedThreadPool(numberOfConcurrentRequests);

        List<CompletableFuture<List<TaskDto>>> futures = new ArrayList<>();

        // Start multiple concurrent requests
        for (int i = 0; i < numberOfConcurrentRequests; i++) {
            CompletableFuture<List<TaskDto>> future = CompletableFuture.supplyAsync(() -> {
                try {
                    startLatch.await(); // Wait for all threads to be ready
                    return taskService.getTodayTasksForOperator(operatorName)
                            .collectList()
                            .block(Duration.ofSeconds(5));
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    return List.of();
                } finally {
                    completionLatch.countDown();
                }
            }, executor);
            futures.add(future);
        }

        // Start all requests simultaneously
        startLatch.countDown();

        // Wait for all requests to complete
        assertTrue(completionLatch.await(10, TimeUnit.SECONDS), "All requests should complete within 10 seconds");

        // Verify all requests returned the same result
        for (CompletableFuture<List<TaskDto>> future : futures) {
            try {
                List<TaskDto> result = future.get(1, TimeUnit.SECONDS);
                assertEquals(1, result.size());
                assertEquals(sampleTaskDto.getId(), result.get(0).getId());
                assertEquals(sampleTaskDto.getName(), result.get(0).getName());
            } catch (Exception e) {
                fail("Request should not fail: " + e.getMessage());
            }
        }

        // Verify that the underlying service was called only once due to deduplication
        // Note: Due to the nature of reactive streams and caching, the exact number of calls
        // may vary, but it should be significantly less than the number of concurrent requests
        verify(plannedWorkService, times(1)).getPlannedWorkForOperatorFromToday(operatorName);

        executor.shutdown();
    }

    @Test
    void testRequestDeduplicationService_CacheExpiry() throws InterruptedException {
        // Test that cache expires after the deduplication duration
        when(plannedWorkService.getPlannedWorkForOperatorFromToday(operatorName))
                .thenReturn(Flux.just(samplePlannedWork));
        when(actualWorkService.getActualWorkByUserNameAndWorkDate(anyString(), any()))
                .thenReturn(Flux.empty());
        when(taskConverter.convertToTaskDto(any(), any()))
                .thenReturn(sampleTaskDto);

        // First request
        StepVerifier.create(taskService.getTodayTasksForOperator(operatorName))
                .expectNext(sampleTaskDto)
                .verifyComplete();

        // Wait for cache to expire (500ms + buffer)
        Thread.sleep(600);

        // Second request after cache expiry
        StepVerifier.create(taskService.getTodayTasksForOperator(operatorName))
                .expectNext(sampleTaskDto)
                .verifyComplete();

        // Verify that the service was called twice (once for each request after cache expiry)
        verify(plannedWorkService, times(2)).getPlannedWorkForOperatorFromToday(operatorName);
    }

    @Test
    void testRequestDeduplicationService_DifferentOperators() {
        // Test that different operators don't share cache
        String operator1 = "Operator1";
        String operator2 = "Operator2";

        when(plannedWorkService.getPlannedWorkForOperatorFromToday(operator1))
                .thenReturn(Flux.just(samplePlannedWork));
        when(plannedWorkService.getPlannedWorkForOperatorFromToday(operator2))
                .thenReturn(Flux.just(samplePlannedWork));
        when(actualWorkService.getActualWorkByUserNameAndWorkDate(anyString(), any()))
                .thenReturn(Flux.empty());
        when(taskConverter.convertToTaskDto(any(), any()))
                .thenReturn(sampleTaskDto);

        // Request for operator1
        StepVerifier.create(taskService.getTodayTasksForOperator(operator1))
                .expectNext(sampleTaskDto)
                .verifyComplete();

        // Request for operator2
        StepVerifier.create(taskService.getTodayTasksForOperator(operator2))
                .expectNext(sampleTaskDto)
                .verifyComplete();

        // Verify that both operators' services were called
        verify(plannedWorkService, times(1)).getPlannedWorkForOperatorFromToday(operator1);
        verify(plannedWorkService, times(1)).getPlannedWorkForOperatorFromToday(operator2);
    }

    @Test
    void testRequestDeduplicationService_ErrorHandling() {
        // Test that errors don't break the deduplication mechanism
        when(plannedWorkService.getPlannedWorkForOperatorFromToday(operatorName))
                .thenReturn(Flux.error(new RuntimeException("Database connection failed")));
        when(actualWorkService.getActualWorkByUserNameAndWorkDate(anyString(), any()))
                .thenReturn(Flux.empty());

        // First request should handle error gracefully
        StepVerifier.create(taskService.getTodayTasksForOperator(operatorName))
                .verifyComplete(); // Should complete empty due to error handling

        // Second request should also work (error shouldn't break the cache mechanism)
        StepVerifier.create(taskService.getTodayTasksForOperator(operatorName))
                .verifyComplete();

        // Verify that the service was called for both requests
        verify(plannedWorkService, times(2)).getPlannedWorkForOperatorFromToday(operatorName);
    }

    @Test
    void testCacheMetrics() {
        // Test that we can monitor cache usage
        assertEquals(0, requestDeduplicationService.getOngoingFluxRequestsCount());

        when(plannedWorkService.getPlannedWorkForOperatorFromToday(operatorName))
                .thenReturn(Flux.just(samplePlannedWork).delayElements(Duration.ofMillis(100)));
        when(actualWorkService.getActualWorkByUserNameAndWorkDate(anyString(), any()))
                .thenReturn(Flux.empty());
        when(taskConverter.convertToTaskDto(any(), any()))
                .thenReturn(sampleTaskDto);

        // Start a request but don't wait for completion
        Flux<TaskDto> result = taskService.getTodayTasksForOperator(operatorName);

        // The request should be in the cache now
        assertEquals(1, requestDeduplicationService.getOngoingFluxRequestsCount());

        // Complete the request
        StepVerifier.create(result)
                .expectNext(sampleTaskDto)
                .verifyComplete();

        // Cache should be cleared after completion
        assertEquals(0, requestDeduplicationService.getOngoingFluxRequestsCount());
    }
}
