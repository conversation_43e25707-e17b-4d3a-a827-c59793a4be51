package com.inspeedia.vanning.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * Configuration class to ensure logging directory exists
 */
@Configuration
public class LoggingConfig {

    private static final Logger logger = LoggerFactory.getLogger(LoggingConfig.class);

    @EventListener(ApplicationReadyEvent.class)
    public void ensureLogDirectoryExists() {
        try {
            Path logsPath = Paths.get("logs");
            if (!Files.exists(logsPath)) {
                Files.createDirectories(logsPath);
                logger.info("Created logs directory: {}", logsPath.toAbsolutePath());
            } else {
                logger.debug("Logs directory already exists: {}", logsPath.toAbsolutePath());
            }
        } catch (Exception e) {
            logger.warn("Failed to create logs directory: {}", e.getMessage());
        }
    }
}
