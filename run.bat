@echo off
setlocal

rem Set working directory to application home
cd /d "%~dp0"

rem Set Java options
set JAVA_OPTS=-Xms512m -Xmx2g -XX:+UseG1GC
set JVM_ARGS=%JAVA_OPTS% -Dio.netty.leakDetection.level=DISABLED
set JVM_ARGS=%JVM_ARGS% -Dio.netty.allocator.type=pooled
set JVM_ARGS=%JVM_ARGS% -Dio.netty.noPreferDirect=true

rem Set Spring Boot profile
rem set SPRING_PROFILES_ACTIVE=dev

rem Set Server Port
set SERVER_PORT=8089

rem Set database connection
set DB_URL=r2dbc:mssql://localhost:1433/vma_db
set DB_USERNAME=mssadmin
set DB_PASSWORD=pass

rem Set logging configuration
set LOGGING_LEVEL_ROOT=INFO
set LOG_PATH=%~dp0logs

rem Create logs directory if it doesn't exist
rem if not exist "%LOG_PATH%" mkdir "%LOG_PATH%"

rem Run the application
java %JAVA_ARGS% ^
rem -Dspring.profiles.active=%SPRING_PROFILES_ACTIVE% ^
rem -Dspring.datasource.url=%DB_URL% ^
rem -Dspring.datasource.username=%DB_USERNAME% ^
rem -Dspring.datasource.password=%DB_PASSWORD% ^
-Dlogging.level.root=%LOGGING_LEVEL_ROOT% ^
-jar build/libs/vma-api-0.0.1-SNAPSHOT.jar

endlocal
