-- VMA Database Migration
-- Adds missing columns to existing tables

-- Add missing columns to actual_work table if they don't exist
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('actual_work') AND name = 'van_gp')
    ALTER TABLE actual_work ADD van_gp NVARCHAR(2) NOT NULL DEFAULT 'AB' CHECK (LEN(van_gp) = 2 AND van_gp = UPPER(van_gp));

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('actual_work') AND name = 'completed')
    ALTER TABLE actual_work ADD completed BIT DEFAULT 0;

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('actual_work') AND name = 'progress_rate')
    ALTER TABLE actual_work ADD progress_rate FLOAT CHECK (progress_rate >= 0.0 AND progress_rate <= 100.0);

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('actual_work') AND name = 'version')
    ALTER TABLE actual_work ADD version BIGINT DEFAULT 0;

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('actual_work') AND name = 'deleted')
    ALTER TABLE actual_work ADD deleted BIT DEFAULT 0;

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('actual_work') AND name = 'created_by')
    ALTER TABLE actual_work ADD created_by NVARCHAR(50);

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('actual_work') AND name = 'updated_by')
    ALTER TABLE actual_work ADD updated_by NVARCHAR(50);

-- Add missing columns to planned_work table if they don't exist
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('planned_work') AND name = 'version')
    ALTER TABLE planned_work ADD version BIGINT DEFAULT 0;

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('planned_work') AND name = 'deleted')
    ALTER TABLE planned_work ADD deleted BIT DEFAULT 0;

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('planned_work') AND name = 'created_by')
    ALTER TABLE planned_work ADD created_by NVARCHAR(50);

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('planned_work') AND name = 'updated_by')
    ALTER TABLE planned_work ADD updated_by NVARCHAR(50);

-- Add sheet_id and row_id columns for Excel order preservation
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('planned_work') AND name = 'sheet_id')
    ALTER TABLE planned_work ADD sheet_id INT;

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('planned_work') AND name = 'row_id')
    ALTER TABLE planned_work ADD row_id INT;

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('planned_work') AND name = 'row_name')
    ALTER TABLE planned_work ADD row_name NVARCHAR(5);

IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('planned_work') AND name = 'test_id')
    ALTER TABLE planned_work DROP COLUMN test_id;