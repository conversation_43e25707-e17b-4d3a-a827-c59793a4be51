package com.inspeedia.vanning.service;

import com.inspeedia.vanning.config.ExcelImportConfig;
import com.inspeedia.vanning.domain.PlannedWork;
import com.inspeedia.vanning.repository.PlannedWorkRepository;
import com.inspeedia.vanning.service.excel.ExcelStreamingReader;
import jakarta.validation.Validator;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DefaultDataBufferFactory;
import org.springframework.http.codec.multipart.FilePart;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDate;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * Unit tests for ExcelImportService
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class ExcelImportServiceTest {

    @Mock
    private PlannedWorkRepository plannedWorkRepository;

    @Mock
    private Validator validator;

    @Mock
    private ExcelImportConfig config;

    @Mock
    private FilePart filePart;

    @Mock
    ExcelStreamingReader excelStreamingReader;

    private ExcelImportService excelImportService;

    @BeforeEach
    void setUp() {
        // Mock the config with default values
        ExcelImportConfig.DefaultValues defaultValues = new ExcelImportConfig.DefaultValues();
        defaultValues.setDeliveryPlatform("A");
        defaultValues.setCollectionPlatform("B");
        defaultValues.setSize("L1");

        ExcelImportConfig.ValidationSettings validationSettings = new ExcelImportConfig.ValidationSettings();
        validationSettings.setVanGpPattern("^[A-Z0-9]{2}$");
        validationSettings.setInvalidVanGpValues(new String[]{"0"});

        ExcelImportConfig.EncodingSettings encodingSettings = new ExcelImportConfig.EncodingSettings();
        encodingSettings.setDefaultCharset("UTF-8");
        encodingSettings.setSupportJapanese(true);

        ExcelImportConfig.ProcessingSettings processingSettings = new ExcelImportConfig.ProcessingSettings();
        processingSettings.setSkipHeaderRows(2);
        processingSettings.setBatchSize(100);
        processingSettings.setConcurrencyLevel(16);

        ExcelImportConfig.AuditSettings auditSettings = new ExcelImportConfig.AuditSettings();
        auditSettings.setCreatedBy("excel-import");
        auditSettings.setUpdatedBy("excel-import");

        ExcelImportConfig.FormatSettings formatSettings = new ExcelImportConfig.FormatSettings();
        formatSettings.setDatePatterns(new String[]{"yyyy-MM-dd", "dd/MM/yyyy", "MM/dd/yyyy", "dd-MM-yyyy"});
        formatSettings.setTimePatterns(new String[]{"HH:mm:ss", "HH:mm", "H:mm", "H:mm:ss"});

        ExcelImportConfig.ErrorMessages errorMessages = new ExcelImportConfig.ErrorMessages();
        errorMessages.setVanGpRequired("Van GP is required");
        errorMessages.setVanGpPatternMismatch("Van GP must match pattern: {0}");
        errorMessages.setDateRequired("{0} is required");
        errorMessages.setDateInvalid("Invalid {0} format: {1}");
        errorMessages.setTimeRequired("{0} is required");
        errorMessages.setTimeInvalid("Invalid {0} format: {1}");
        errorMessages.setConstraintViolation("Constraint violation while saving record: {0}");
        errorMessages.setTransientDbError("Transient database error (retryable): {0}");
        errorMessages.setTimeoutError("Database operation timed out: {0}");
        errorMessages.setGeneralDbError("Database error: {0}");
        errorMessages.setUnknownDbError("Unknown database error");

        when(config.getHeaderRowIndex()).thenReturn(1);
        when(config.getDataStartRowIndex()).thenReturn(2);
        when(config.getDefaultValues()).thenReturn(defaultValues);
        when(config.getValidation()).thenReturn(validationSettings);
        when(config.getEncoding()).thenReturn(encodingSettings);
        when(config.getProcessing()).thenReturn(processingSettings);
        when(config.getAudit()).thenReturn(auditSettings);
        when(config.getFormats()).thenReturn(formatSettings);
        when(config.getErrors()).thenReturn(errorMessages);

        // Mock file part
        when(filePart.filename()).thenReturn("計画.xlsx");

        excelImportService = new ExcelImportService(plannedWorkRepository, validator, config, excelStreamingReader);
    }

    private byte[] loadJapaneseExcelFile() throws IOException {
        ClassPathResource resource = new ClassPathResource("計画.xlsx");
        try (InputStream inputStream = resource.getInputStream()) {
            return inputStream.readAllBytes();
        }
    }

    @Test
    void testImportPlannedWorkFromExcel_Success() throws IOException {
        // Arrange
        byte[] excelData = loadJapaneseExcelFile();
        DataBuffer dataBuffer = new DefaultDataBufferFactory().wrap(excelData);

        when(filePart.content()).thenReturn(Flux.just(dataBuffer));
        when(validator.validate(any(PlannedWork.class))).thenReturn(Collections.emptySet());
        when(plannedWorkRepository.findByWorkDateAndVanGpAndOperatorName(
                any(LocalDate.class), anyString(), anyString()))
                .thenReturn(Flux.empty());
        when(plannedWorkRepository.save(any(PlannedWork.class)))
                .thenAnswer(invocation -> Mono.just(invocation.getArgument(0)));

        // Mock the ExcelStreamingReader to return empty flux (no rows to process)
        when(excelStreamingReader.read(any())).thenReturn(Flux.empty());

        // Act & Assert
        StepVerifier.create(excelImportService.importPlannedWorkFromExcel(filePart))
                .assertNext(result -> {
                    assertNotNull(result);
                    assertTrue(result.getTotalRecords() >= 0);
                })
                .verifyComplete();
    }

    @Test
    void testImportPlannedWorkFromExcel_InvalidFile() {
        // Arrange
        when(filePart.content()).thenReturn(Flux.error(new RuntimeException("Invalid file")));

        // Act & Assert
        StepVerifier.create(excelImportService.importPlannedWorkFromExcel(filePart))
                .expectError(RuntimeException.class)
                .verify();
    }

}
