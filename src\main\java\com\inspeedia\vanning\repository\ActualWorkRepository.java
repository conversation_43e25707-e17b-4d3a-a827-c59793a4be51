package com.inspeedia.vanning.repository;

import com.inspeedia.vanning.domain.ActualWork;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDate;
import java.time.LocalTime;

/**
 * Repository interface for ActualWork entity
 *
 * This repository provides reactive database operations for ActualWork entities
 * using R2DBC with MSSQL database.
 */
@Repository
public interface ActualWorkRepository extends R2dbcRepository<ActualWork, Long> {

    /**
     * Find actual work by operator name
     */
    Flux<ActualWork> findByOperatorName(String operatorName);

    /**
     * Find actual work by progress
     */
    Flux<ActualWork> findByProgress(Integer progress);

    /**
     * Find actual work by progress range
     */
    @Query("SELECT * FROM actual_work WHERE deleted = 0 AND progress BETWEEN :minProgress AND :maxProgress ORDER BY progress DESC")
    Flux<ActualWork> findByProgressBetween(@Param("minProgress") Integer minProgress, @Param("maxProgress") Integer maxProgress);

    /**
     * Find actual work by progress rate range
     */
    @Query("SELECT * FROM actual_work WHERE deleted = 0 AND progress_rate BETWEEN :minRate AND :maxRate ORDER BY progress_rate DESC")
    Flux<ActualWork> findByProgressRateBetween(@Param("minRate") Float minRate, @Param("maxRate") Float maxRate);

    /**
     * Find actual work by start time range
     */
    @Query("SELECT * FROM actual_work WHERE deleted = 0 AND start_time BETWEEN :startTime AND :endTime ORDER BY start_time")
    Flux<ActualWork> findByStartTimeBetween(@Param("startTime") LocalTime startTime, @Param("endTime") LocalTime endTime);

    /**
     * Search actual work by user name
     */
    @Query("SELECT * FROM actual_work WHERE deleted = 0 AND "
            + "LOWER(operator_name) LIKE LOWER(CONCAT('%', :searchTerm, '%')) "
            + "ORDER BY operator_name")
    Flux<ActualWork> searchByUserName(@Param("searchTerm") String searchTerm);

    /**
     * Find high progress work (progress >= 8)
     */
    @Query("SELECT * FROM actual_work WHERE deleted = 0 AND progress >= 8 ORDER BY progress DESC")
    Flux<ActualWork> findHighProgressWork();

    /**
     * Find low progress work (progress <= 3)
     */
    @Query("SELECT * FROM actual_work WHERE deleted = 0 AND progress <= 3 ORDER BY progress ASC")
    Flux<ActualWork> findLowProgressWork();

    /**
     * Count actual work by user name
     */
    @Query("SELECT COUNT(*) FROM actual_work WHERE deleted = 0 AND operator_name = :userName")
    Mono<Long> countByUserName(@Param("userName") String userName);

    /**
     * Find work by duration range
     */
    @Query("SELECT * FROM actual_work WHERE deleted = 0 AND duration BETWEEN :minDuration AND :maxDuration ORDER BY duration DESC")
    Flux<ActualWork> findByDurationBetween(@Param("minDuration") LocalTime minDuration, @Param("maxDuration") LocalTime maxDuration);

    /**
     * Soft delete actual work by ID
     */
    @Query("UPDATE actual_work SET deleted = 1, updated_at = GETDATE() WHERE id = :id")
    Mono<Integer> softDeleteById(@Param("id") Long id);

    /**
     * Find all non-deleted actual work with pagination
     */
    @Query("SELECT * FROM actual_work WHERE deleted = 0 ORDER BY operator_name OFFSET :offset ROWS FETCH NEXT :limit ROWS ONLY")
    Flux<ActualWork> findAllActive(@Param("offset") Long offset, @Param("limit") Integer limit);

    /**
     * Find actual work by work date
     */
    @Query("SELECT * FROM actual_work WHERE deleted = 0 AND work_date = :workDate ORDER BY start_time")
    Flux<ActualWork> findByWorkDate(@Param("workDate") LocalDate workDate);

    /**
     * Find today's planned work
     */
    @Query("SELECT * FROM actual_work WHERE deleted = 0 AND [work_date] = CAST(GETDATE() AS DATE)")
    Flux<ActualWork> findWorkByTodayDate();

    /**
     * Find actual work by work date range
     */
    @Query("SELECT * FROM actual_work WHERE deleted = 0 AND work_date BETWEEN :startDate AND :endDate ORDER BY work_date, start_time")
    Flux<ActualWork> findByWorkDateBetween(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * Find actual work by operator name and work date
     */
    @Query("SELECT * FROM actual_work WHERE deleted = 0 AND operator_name = $1 AND work_date = $2 ORDER BY start_time")
    Flux<ActualWork> findByOperatorNameAndWorkDate(String operatorName, LocalDate workDate);
}
