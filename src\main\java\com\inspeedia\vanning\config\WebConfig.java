package com.inspeedia.vanning.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.reactive.CorsWebFilter;
import org.springframework.web.cors.reactive.UrlBasedCorsConfigurationSource;
import org.springframework.web.reactive.config.EnableWebFlux;
import org.springframework.web.reactive.config.WebFluxConfigurer;

/**
 * Web configuration for reactive Spring WebFlux
 *
 * This configuration class sets up CORS, content negotiation, and other
 * web-related configurations.
 */
@Configuration
@EnableWebFlux
public class WebConfig implements WebFluxConfigurer {

    private final Logger log = LoggerFactory.getLogger(WebConfig.class);

    private final AppProperties appProperties;

    public WebConfig(AppProperties appProperties) {
        this.appProperties = appProperties;
    }

    /**
     * Configures CORS for the application
     *
     * @return CorsWebFilter configured with application properties
     */
    @Bean
    public CorsWebFilter corsWebFilter() {
        CorsConfiguration corsConfig = new CorsConfiguration();
        corsConfig.setAllowedOriginPatterns(appProperties.getCors().getAllowedOrigins());
        corsConfig.setAllowedMethods(appProperties.getCors().getAllowedMethods());
        corsConfig.setAllowedHeaders(appProperties.getCors().getAllowedHeaders());
        corsConfig.setAllowCredentials(appProperties.getCors().isAllowCredentials());
        corsConfig.setMaxAge(appProperties.getCors().getMaxAge());

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", corsConfig);

        log.info("CORS configured with origins: {}, methods: {}",
                appProperties.getCors().getAllowedOrigins(),
                appProperties.getCors().getAllowedMethods());
        return new CorsWebFilter(source);
    }
}
