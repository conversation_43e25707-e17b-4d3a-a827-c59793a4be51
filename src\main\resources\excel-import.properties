# Excel Import Configuration
# This file contains all configurable settings for Excel import functionality

# Row Configuration
excel.import.header-row-index=1
excel.import.data-start-row-index=2

# Processing Configuration
excel.import.processing.skip-header-rows=2
excel.import.processing.batch-size=100
excel.import.processing.concurrency-level=16

# Default Values for Missing Fields
excel.import.default-values.delivery-platform=A
excel.import.default-values.collection-platform=B
excel.import.default-values.size=L1

# Validation Settings
excel.import.validation.van-gp-pattern=^[A-Z0-9]{2}$
excel.import.validation.van-gp-min-length=2
excel.import.validation.van-gp-max-length=2
excel.import.validation.invalid-van-gp-values=0

# Audit Settings
excel.import.audit.created-by=excel-import
excel.import.audit.updated-by=excel-import

# Date Format Settings
excel.import.formats.date-patterns=yyyy-MM-dd,dd/MM/yyyy,MM/dd/yyyy,dd-MM-yyyy

# Time Format Settings
excel.import.formats.time-patterns=HH:mm:ss,HH:mm,H:mm,H:mm:ss

# Character Encoding Settings
excel.import.encoding.default-charset=UTF-8
excel.import.encoding.support-japanese=true

# Error Messages
excel.import.errors.van-gp-required=Van GP is required
excel.import.errors.van-gp-pattern-mismatch=Van GP must match pattern: {0}
excel.import.errors.date-required={0} is required
excel.import.errors.date-invalid=Invalid {0} format: {1}
excel.import.errors.time-required={0} is required
excel.import.errors.time-invalid=Invalid {0} format: {1}
excel.import.errors.constraint-violation=Constraint violation while saving record: {0}
excel.import.errors.transient-db-error=Transient database error (retryable): {0}
excel.import.errors.timeout-error=Database operation timed out: {0}
excel.import.errors.general-db-error=Database error: {0}
excel.import.errors.unknown-db-error=Unknown database error
