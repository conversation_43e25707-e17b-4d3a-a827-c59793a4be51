package com.inspeedia.vanning.domain.alert.validator.impl;

import com.inspeedia.vanning.config.AlertProperties;
import com.inspeedia.vanning.domain.ActualWork;
import com.inspeedia.vanning.domain.PlannedWork;
import com.inspeedia.vanning.domain.alert.AlertType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ProgressDelayAlertValidatorTest {

    @Mock
    private AlertProperties alertProperties;

    private ProgressDelayAlertValidator validator;
    private PlannedWork plannedWork;
    private ActualWork actualWork;

    @BeforeEach
    void setUp() {
        validator = new ProgressDelayAlertValidator(alertProperties);

        // Set up planned work
        plannedWork = new PlannedWork();
        plannedWork.setOperatorName("TestUser");
        plannedWork.setWorkDate(LocalDate.of(2024, 1, 15));
        plannedWork.setStartTime(LocalTime.of(8, 0));
        plannedWork.setEndTime(LocalTime.of(17, 0));
        plannedWork.setVanGp("AB");

        // Set up actual work
        actualWork = new ActualWork();
        actualWork.setOperatorName("TestUser");
        actualWork.setWorkDate(LocalDate.of(2024, 1, 15));
        actualWork.setStartTime(LocalTime.of(8, 0));
        actualWork.setEndTime(LocalTime.of(18, 0)); // 1 hour late
        actualWork.setProgressRate(30.0f); // Below threshold
        actualWork.setVanGp("AB");
    }

    @Test
    void testHasAlert_WithValidProgressRate_ShouldReturnTrue() {
        // Given
        when(alertProperties.getEnabled()).thenReturn(Map.of("LOW_PROGRESS", true));
        when(alertProperties.getThresholds()).thenReturn(Map.of("low-progress-minutes", 40));
        when(alertProperties.getRateThresholds()).thenReturn(Map.of("low-progress", 40.0f));

        // When
        boolean result = validator.hasAlert(plannedWork, actualWork);

        // Then
        assertTrue(result, "Should have alert when progress rate is below threshold and end time is late");
    }

    @Test
    void testHasAlert_WithNullProgressRate_ShouldReturnFalse() {
        // Given
        actualWork.setProgressRate(null); // Null progress rate
        when(alertProperties.getEnabled()).thenReturn(Map.of("LOW_PROGRESS", true));
        when(alertProperties.getThresholds()).thenReturn(Map.of("low-progress-minutes", 40));
        when(alertProperties.getRateThresholds()).thenReturn(Map.of("low-progress", 40.0f));

        // When
        boolean result = validator.hasAlert(plannedWork, actualWork);

        // Then
        assertFalse(result, "Should not have alert when progress rate is null");
    }

    @Test
    void testHasAlert_WithHighProgressRate_ShouldReturnFalse() {
        // Given
        actualWork.setProgressRate(80.0f); // Above threshold
        when(alertProperties.getEnabled()).thenReturn(Map.of("LOW_PROGRESS", true));
        when(alertProperties.getThresholds()).thenReturn(Map.of("low-progress-minutes", 40));
        when(alertProperties.getRateThresholds()).thenReturn(Map.of("low-progress", 40.0f));

        // When
        boolean result = validator.hasAlert(plannedWork, actualWork);

        // Then
        assertFalse(result, "Should not have alert when progress rate is above threshold");
    }

    @Test
    void testHasAlert_WithDisabledAlert_ShouldReturnFalse() {
        // Given
        when(alertProperties.getEnabled()).thenReturn(Map.of("LOW_PROGRESS", false));
        // No need to stub thresholds when alert is disabled

        // When
        boolean result = validator.hasAlert(plannedWork, actualWork);

        // Then
        assertFalse(result, "Should not have alert when alert is disabled");
    }

    @Test
    void testHasAlert_WithNullActualWork_ShouldReturnFalse() {
        // Given
        when(alertProperties.getEnabled()).thenReturn(Map.of("LOW_PROGRESS", true));
        when(alertProperties.getThresholds()).thenReturn(Map.of("low-progress-minutes", 40));
        when(alertProperties.getRateThresholds()).thenReturn(Map.of("low-progress", 40.0f));

        // When
        boolean result = validator.hasAlert(plannedWork, null);

        // Then
        assertFalse(result, "Should not have alert when actual work is null");
    }

    @Test
    void testHasAlert_WithNullPlannedWork_ShouldReturnFalse() {
        // Given
        when(alertProperties.getEnabled()).thenReturn(Map.of("LOW_PROGRESS", true));
        when(alertProperties.getThresholds()).thenReturn(Map.of("low-progress-minutes", 40));
        when(alertProperties.getRateThresholds()).thenReturn(Map.of("low-progress", 40.0f));

        // When
        boolean result = validator.hasAlert(null, actualWork);

        // Then
        assertFalse(result, "Should not have alert when planned work is null");
    }

    @Test
    void testHasAlert_WithNullEndTimes_ShouldReturnFalse() {
        // Given
        actualWork.setEndTime(null);
        plannedWork.setEndTime(null);
        when(alertProperties.getEnabled()).thenReturn(Map.of("LOW_PROGRESS", true));
        when(alertProperties.getThresholds()).thenReturn(Map.of("low-progress-minutes", 40));
        when(alertProperties.getRateThresholds()).thenReturn(Map.of("low-progress", 40.0f));

        // When
        boolean result = validator.hasAlert(plannedWork, actualWork);

        // Then
        assertFalse(result, "Should not have alert when end times are null");
    }

    @Test
    void testGetAlertType() {
        assertEquals(AlertType.LOW_PROGRESS, validator.getAlertType());
    }

    @Test
    void testGetAlertDescription() {
        String description = validator.getAlertDescription();
        assertNotNull(description);
        assertEquals(AlertType.LOW_PROGRESS.getDescription(), description);
    }
}
