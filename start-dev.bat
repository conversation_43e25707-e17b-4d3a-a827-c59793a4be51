@echo off
setlocal

rem Set working directory to application home
cd /d "%~dp0"

rem Create logs directory if it doesn't exist
if not exist "logs" mkdir "logs"

rem Set Java options
set JAVA_OPTS=-Xms512m -Xmx1024m

rem Set Spring Boot profile
set SPRING_PROFILES_ACTIVE=dev

rem Set database connection
set DB_URL=r2dbc:mssql://localhost:1433/vma_db
set DB_USERNAME=mssadmin
set DB_PASSWORD=pass

rem Set logging configuration
set LOGGING_LEVEL_ROOT=INFO

echo Starting VMA API in development mode...
echo Logs will be written to: %~dp0logs

rem Run the application with bootRun
call gradlew bootRun

endlocal
