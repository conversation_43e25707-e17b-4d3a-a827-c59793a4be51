package com.inspeedia.vanning.dto;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

import java.util.List;

/**
 * Test class to verify progressRate field functionality in TaskDto
 */
class TaskDtoProgressRateTest {

    @Test
    void testProgressRateGetterSetter() {
        TaskDto taskDto = new TaskDto();
        
        // Test setting and getting progressRate
        Float expectedProgressRate = 85.5f;
        taskDto.setProgressRate(expectedProgressRate);
        
        assertEquals(expectedProgressRate, taskDto.getProgressRate());
    }

    @Test
    void testProgressRateWithNullValue() {
        TaskDto taskDto = new TaskDto();
        
        // Test setting null progressRate
        taskDto.setProgressRate(null);
        
        assertNull(taskDto.getProgressRate());
    }

    @Test
    void testProgressRateInConstructor() {
        Float progressRate = 92.3f;
        
        TaskDto taskDto = new TaskDto(
            1L, "Test User", "T001", "2024-01-15", "AB",
            "07:30", "08:00", "17:00", "9h",
            "08:15", "17:30", "9h 15m", 85,
            progressRate, List.of()
        );
        
        assertEquals(progressRate, taskDto.getProgressRate());
    }

    @Test
    void testToStringIncludesProgressRate() {
        TaskDto taskDto = new TaskDto();
        taskDto.setId(1L);
        taskDto.setProgressRate(75.8f);
        
        String toString = taskDto.toString();
        
        assertTrue(toString.contains("progressRate='75.8'"));
    }
}
