package com.inspeedia.vanning.domain.alert.validator.impl;

import org.springframework.stereotype.Component;

import com.inspeedia.vanning.domain.ActualWork;
import com.inspeedia.vanning.domain.PlannedWork;
import com.inspeedia.vanning.config.AlertProperties;
import com.inspeedia.vanning.domain.alert.AlertType;
import com.inspeedia.vanning.domain.alert.validator.AbstractAlertValidator;

@Component
public class ProgressDelayAlertValidator extends AbstractAlertValidator {

    public final AlertProperties alertProperties;

    public ProgressDelayAlertValidator(AlertProperties alertProperties) {
        super(AlertType.LOW_PROGRESS, 2);
        this.alertProperties = alertProperties;
    }

    @Override
    public boolean hasAlert(PlannedWork plannedWork, ActualWork actualWork) {
        if (!alertProperties.getEnabled().getOrDefault(alertType.name(), false)) {
            return false;
        }
        int threshold = alertProperties.getThresholds().getOrDefault("low-progress-minutes", 40);
        float progressThreshold = alertProperties.getRateThresholds().getOrDefault("low-progress", 40.0f);
        return actualWork != null && plannedWork != null
                && actualWork.getEndTime() != null && plannedWork.getEndTime() != null
                && actualWork.getProgressRate() != null
                && actualWork.getProgressRate() < progressThreshold
                && actualWork.getEndTime().isAfter(plannedWork.getEndTime().plusMinutes(threshold));
    }

    @Override
    public String getAlertDescription() {
        return alertType.getDescription();
    }
}
