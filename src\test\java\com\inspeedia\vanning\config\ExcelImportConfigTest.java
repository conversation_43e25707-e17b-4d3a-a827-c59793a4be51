package com.inspeedia.vanning.config;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class to verify that ExcelImportConfig properly loads all configuration
 * properties
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE)
@ActiveProfiles("test")
@TestPropertySource(locations = "classpath:excel-import.properties")
class ExcelImportConfigTest {

    @Autowired
    private ExcelImportConfig config;

    @Test
    void testBasicConfiguration() {
        assertNotNull(config);
        assertEquals(1, config.getHeaderRowIndex());
        assertEquals(2, config.getDataStartRowIndex());
    }

    @Test
    void testDefaultValues() {
        ExcelImportConfig.DefaultValues defaultValues = config.getDefaultValues();
        assertNotNull(defaultValues);
        assertEquals("A", defaultValues.getDeliveryPlatform());
        assertEquals("B", defaultValues.getCollectionPlatform());
        assertEquals("L1", defaultValues.getSize());
    }

    @Test
    void testValidationSettings() {
        ExcelImportConfig.ValidationSettings validation = config.getValidation();
        assertNotNull(validation);
        assertEquals("^[A-Z0-9]{2}$", validation.getVanGpPattern());
        assertEquals(2, validation.getVanGpMinLength());
        assertEquals(2, validation.getVanGpMaxLength());
        assertArrayEquals(new String[]{"0"}, validation.getInvalidVanGpValues());
    }

    @Test
    void testEncodingSettings() {
        ExcelImportConfig.EncodingSettings encoding = config.getEncoding();
        assertNotNull(encoding);
        assertEquals("UTF-8", encoding.getDefaultCharset());
        assertTrue(encoding.isSupportJapanese());
    }

    @Test
    void testProcessingSettings() {
        ExcelImportConfig.ProcessingSettings processing = config.getProcessing();
        assertNotNull(processing);
        assertEquals(2, processing.getSkipHeaderRows());
        assertEquals(100, processing.getBatchSize());
        assertEquals(16, processing.getConcurrencyLevel());
    }

    @Test
    void testAuditSettings() {
        ExcelImportConfig.AuditSettings audit = config.getAudit();
        assertNotNull(audit);
        assertEquals("excel-import", audit.getCreatedBy());
        assertEquals("excel-import", audit.getUpdatedBy());
    }

    @Test
    void testFormatSettings() {
        ExcelImportConfig.FormatSettings formats = config.getFormats();
        assertNotNull(formats);

        String[] expectedDatePatterns = {"yyyy-MM-dd", "dd/MM/yyyy", "MM/dd/yyyy", "dd-MM-yyyy"};
        assertArrayEquals(expectedDatePatterns, formats.getDatePatterns());

        String[] expectedTimePatterns = {"HH:mm:ss", "HH:mm", "H:mm", "H:mm:ss"};
        assertArrayEquals(expectedTimePatterns, formats.getTimePatterns());
    }

    @Test
    void testErrorMessages() {
        ExcelImportConfig.ErrorMessages errors = config.getErrors();
        assertNotNull(errors);
        assertEquals("Van GP is required", errors.getVanGpRequired());
        assertEquals("Van GP must match pattern: {0}", errors.getVanGpPatternMismatch());
        assertEquals("{0} is required", errors.getDateRequired());
        assertEquals("Invalid {0} format: {1}", errors.getDateInvalid());
        assertEquals("{0} is required", errors.getTimeRequired());
        assertEquals("Invalid {0} format: {1}", errors.getTimeInvalid());
        assertEquals("Constraint violation while saving record: {0}", errors.getConstraintViolation());
        assertEquals("Transient database error (retryable): {0}", errors.getTransientDbError());
        assertEquals("Database operation timed out: {0}", errors.getTimeoutError());
        assertEquals("Database error: {0}", errors.getGeneralDbError());
        assertEquals("Unknown database error", errors.getUnknownDbError());
    }
}
