# VMA API - Vanning Management Application

A reactive Spring Boot API for managing actual work and planned work using WebFlux, R2DBC, and MSSQL Server.

## Features

- **Reactive Programming**: Built with Spring WebFlux for non-blocking, reactive operations
- **Database**: R2DBC with MSSQL Server for reactive database operations
- **Resilient Migration System**: Robust database migration with graceful error handling
- **Excel Import**: Advanced Excel file processing with validation and error reporting
- **Security**: Basic authentication with configurable users
- **Documentation**: OpenAPI 3.0 with Swagger UI
- **Monitoring**: Actuator endpoints with Prometheus metrics
- **Logging**: Structured JSON logging with Logback and file rotation
- **Exception Handling**: Global exception handler with standardized error responses
- **Validation**: Bean validation with detailed error messages
- **Testing**: Comprehensive test suite with 100% success rate

## Technology Stack

- **Java 17**
- **Spring Boot 3.5.5**
- **Spring WebFlux** (Reactive Web)
- **Spring Data R2DBC** (Reactive Database Access)
- **MSSQL Server** (Database)
- **Gradle 8.5** (Build Tool)
- **OpenAPI 3.0** (API Documentation)
- **Apache POI** (Excel Processing)
- **Logback** (Logging Framework)

## Prerequisites

- Java 17 or higher
- MSSQL Server 2019 or higher
- Gradle 8.5 or higher (or use the wrapper)

## Quick Start

### 1. Database Setup

Create a database named `vma_db` in your MSSQL Server instance. The application will automatically handle schema creation and migration.

### 2. Configuration

Update the database connection in `src/main/resources/application.yml`:

```yaml
spring:
  r2dbc:
    url: r2dbc:mssql://localhost:1433/vma_db
    username: your_username
    password: your_password
```

### 3. Build and Run

```bash
# Build the application
./gradlew build

# Run the application (development mode)
./gradlew bootRun

# Or use the convenience script
./start-dev.bat
```

The application will start on port 8089 (configurable via SERVER_PORT environment variable).

### 4. Database Migration

The application includes a resilient database migration system that:

- Automatically creates tables if they don't exist
- Adds missing columns without breaking existing data
- Handles column renames gracefully
- Provides detailed logging for all migration operations
- Continues operation even if some migrations fail

## API Documentation

Once the application is running, you can access:

- **Swagger UI**: http://localhost:8089/swagger-ui.html
- **OpenAPI JSON**: http://localhost:8089/api-docs
- **Actuator Health**: http://localhost:8089/actuator/health
- **Prometheus Metrics**: http://localhost:8089/actuator/prometheus

## Authentication

The API uses basic authentication with the following default credentials:

- **Admin**: username=`admin`, password=`admin123`
- **User**: username=`user`, password=`user123`

## API Endpoints

### Actual Work Management

- `GET /api/v1/actual-work` - Get all active actual work records
- `POST /api/v1/actual-work` - Create a new actual work record
- `GET /api/v1/actual-work/{id}` - Get actual work by ID
- `PUT /api/v1/actual-work/{id}` - Update actual work
- `DELETE /api/v1/actual-work/{id}` - Delete actual work
- `GET /api/v1/actual-work/user/{userName}` - Get actual work by user name
- `GET /api/v1/actual-work/search?q={term}` - Search actual work by user name
- `GET /api/v1/actual-work/progress/{progress}` - Get actual work by progress
- `GET /api/v1/actual-work/high-progress` - Get high progress work (>= 8)
- `GET /api/v1/actual-work/low-progress` - Get low progress work (<= 3)

### Planned Work Management

- `GET /api/v1/planned-work` - Get all active planned work records
- `POST /api/v1/planned-work` - Create a new planned work record
- `GET /api/v1/planned-work/{id}` - Get planned work by ID
- `PUT /api/v1/planned-work/{id}` - Update planned work
- `DELETE /api/v1/planned-work/{id}` - Delete planned work
- `GET /api/v1/planned-work/date/{date}` - Get planned work by date
- `GET /api/v1/planned-work/van-gp/{vanGp}` - Get planned work by van GP
- `GET /api/v1/planned-work/today` - Get today's planned work
- `GET /api/v1/planned-work/upcoming` - Get upcoming planned work

## Excel Import Feature

The application supports importing Excel files with advanced features:

- **Flexible Column Mapping**: Automatically maps Excel columns to database fields
- **Data Validation**: Comprehensive validation with detailed error reporting
- **Batch Processing**: Efficient processing of large Excel files
- **Error Handling**: Graceful handling of malformed data with detailed error messages
- **Japanese Character Support**: Full support for Japanese text in Excel files

See `EXCEL_IMPORT_GUIDE.md` and `EXCEL_CONFIGURATION_GUIDE.md` for detailed documentation.

## Configuration Profiles

- **Development**: `spring.profiles.active=dev`
- **Production**: `spring.profiles.active=prod`
- **Docker**: `spring.profiles.active=docker`
- **Test**: `spring.profiles.active=test` (automatically used during testing)

## Monitoring and Logging

The application includes comprehensive monitoring and logging:

- **Actuator endpoints** for health checks and metrics
- **Prometheus metrics** at `/actuator/prometheus`
- **Structured JSON logging** with file rotation (logs stored in `logs/` directory)
- **Migration tracking** with detailed execution logs
- **Global exception handling** with standardized error responses
- **Request/Response logging** for debugging

## Testing

The application has a comprehensive test suite with 100% success rate:

```bash
# Run all tests
./gradlew test

# Run tests with coverage
./gradlew test jacocoTestReport

# Run specific test class
./gradlew test --tests "ActualWorkServiceTest"

# Clean build and test
./gradlew clean test
```

## Building for Production

```bash
# Build JAR file
./gradlew bootJar

# The JAR will be created in build/libs/
java -jar build/libs/vma-api-0.0.1-SNAPSHOT.jar

# Build with Docker
docker build -t vma-api .

# Run with Docker Compose (includes MSSQL Server)
docker-compose up -d
```

## Architecture

The application follows industry-standard patterns:

- **Layered Architecture**: Controller → Service → Repository
- **Reactive Programming**: Non-blocking I/O with Reactor
- **Domain-Driven Design**: Clear domain models and boundaries
- **SOLID Principles**: Clean, maintainable code structure
- **Resilient Design**: Graceful error handling and recovery
- **Migration-First**: Database changes handled through versioned migrations

## Database Migration System

The application includes a sophisticated migration system:

### Features

- **Automatic Schema Creation**: Creates tables and indexes if they don't exist
- **Column Management**: Adds missing columns and renames existing ones
- **Migration Tracking**: Tracks executed migrations to prevent re-execution
- **Error Recovery**: Continues operation even if individual migrations fail
- **Detailed Logging**: Comprehensive logging of all migration operations

### Migration Process

1. **Connection Check**: Verifies database connectivity
2. **Tracking Setup**: Creates migration tracking table
3. **Schema Migrations**: Creates tables, adds columns, creates indexes
4. **Data Migrations**: Handles data transformations and legacy column renames
5. **Error Handling**: Logs errors but allows application to continue

### Migration Files

- Migration logic is implemented in `DatabaseMigrationService`
- Legacy SQL files (`migration.sql`, `schema.sql`) are preserved for reference
- All migrations are now handled programmatically for better error handling

## Contributing

Please read `CONTRIBUTING.md` for detailed guidelines on:

- Git workflow and branching strategy
- Commit message standards
- Code quality requirements
- Pull request process

Quick start:

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Follow the coding standards and commit message format
4. Ensure all tests pass
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
