package com.inspeedia.vanning.domain.alert.validator;

import com.inspeedia.vanning.domain.alert.AlertType;

public abstract class AbstractAlertValidator implements AlertValidator {
    protected final AlertType alertType;
    protected final int priority;

    protected AbstractAlertValidator(AlertType alertType, int priority) {
        this.alertType = alertType;
        this.priority = priority;
    }

    @Override
    public AlertType getAlertType() {
        return alertType;
    }

    @Override
    public int getPriority() {
        return priority;
    }

    @Override
    public abstract String getAlertDescription();
}
