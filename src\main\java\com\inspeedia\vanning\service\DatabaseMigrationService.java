package com.inspeedia.vanning.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.r2dbc.core.DatabaseClient;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;

/**
 * Service for handling database schema creation, migration, backup, and
 * rollback operations
 */
@Service
public class DatabaseMigrationService {

    private static final Logger log = LoggerFactory.getLogger(DatabaseMigrationService.class);

    private final DatabaseClient databaseClient;
    private final String backupPath;
    private final boolean backupEnabled;
    private final boolean migrationEnabled;
    private final boolean rollbackOnError;
    private final boolean backupBeforeMigration;

    public DatabaseMigrationService(
            DatabaseClient databaseClient,
            @Value("${app.database.backup.enabled:true}") boolean backupEnabled,
            @Value("${app.database.backup.path:./backups}") String backupPath,
            @Value("${app.database.migration.enabled:true}") boolean migrationEnabled,
            @Value("${app.database.migration.rollback-on-error:true}") boolean rollbackOnError,
            @Value("${app.database.migration.backup-before-migration:true}") boolean backupBeforeMigration) {
        this.databaseClient = databaseClient;
        this.backupEnabled = backupEnabled;
        this.backupPath = backupPath;
        this.migrationEnabled = migrationEnabled;
        this.rollbackOnError = rollbackOnError;
        this.backupBeforeMigration = backupBeforeMigration;
    }

    /**
     * Run complete database migration process
     */
    public Mono<Void> runMigrations() {
        if (!migrationEnabled) {
            log.info("Database migration is disabled");
            return Mono.empty();
        }

        log.info("Starting database migration process");

        return createBackupDirectory()
                .then(checkTablesExist())
                .flatMap(tablesExist -> {
                    if (tablesExist && backupBeforeMigration) {
                        log.info("Tables exist, creating backup before migration");
                        return backupExistingData()
                                .then(runSchemaAndMigration());
                    } else {
                        log.info("Tables don't exist or backup disabled, running schema creation");
                        return runSchemaAndMigration();
                    }
                })
                .doOnSuccess(unused -> log.info("Database migration completed successfully"))
                .doOnError(error -> {
                    log.error("Database migration failed: {}", error.getMessage(), error);
                    if (rollbackOnError) {
                        log.info("Attempting rollback due to migration failure");
                        // Note: Rollback implementation would go here
                    }
                });
    }

    /**
     * Create backup directory if it doesn't exist
     */
    private Mono<Void> createBackupDirectory() {
        return Mono.fromCallable(() -> {
            Path backupDir = Paths.get(backupPath);
            if (!Files.exists(backupDir)) {
                Files.createDirectories(backupDir);
                log.info("Created backup directory: {}", backupPath);
            }
            return null;
        }).then();
    }

    /**
     * Check if tables already exist
     */
    private Mono<Boolean> checkTablesExist() {
        return databaseClient.sql("""
                SELECT COUNT(*) as table_count 
                FROM sysobjects 
                WHERE name IN ('planned_work', 'actual_work') AND xtype='U'
                """)
                .fetch()
                .first()
                .map(result -> {
                    Number count = (Number) result.get("table_count");
                    boolean exist = count != null && count.intValue() > 0;
                    log.info("Tables exist check: {}", exist);
                    return exist;
                })
                .onErrorReturn(false);
    }

    /**
     * Backup existing data before migration
     */
    private Mono<Void> backupExistingData() {
        if (!backupEnabled) {
            log.info("Backup is disabled, skipping data backup");
            return Mono.empty();
        }

        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));

        return backupTable("planned_work", timestamp)
                .then(backupTable("actual_work", timestamp))
                .doOnSuccess(unused -> log.info("Data backup completed with timestamp: {}", timestamp));
    }

    /**
     * Backup a specific table
     */
    private Mono<Void> backupTable(String tableName, String timestamp) {
        return databaseClient.sql("SELECT COUNT(*) as row_count FROM " + tableName + " WHERE deleted = 0")
                .fetch()
                .first()
                .flatMap(result -> {
                    Number count = (Number) result.get("row_count");
                    if (count == null || count.intValue() == 0) {
                        log.info("Table {} is empty, skipping backup", tableName);
                        return Mono.empty();
                    }

                    log.info("Backing up {} rows from table {}", count, tableName);
                    return databaseClient.sql("SELECT * FROM " + tableName + " WHERE deleted = 0")
                            .fetch()
                            .all()
                            .collectList()
                            .flatMap(rows -> saveBackupToFile(tableName, timestamp, rows));
                })
                .onErrorResume(error -> {
                    log.warn("Failed to backup table {}: {}", tableName, error.getMessage());
                    return Mono.empty();
                });
    }

    /**
     * Save backup data to file
     */
    private Mono<Void> saveBackupToFile(String tableName, String timestamp, List<?> rows) {
        return Mono.fromCallable(() -> {
            String fileName = String.format("%s_backup_%s.json", tableName, timestamp);
            Path backupFile = Paths.get(backupPath, fileName);

            // Simple JSON-like format for backup
            StringBuilder content = new StringBuilder();
            content.append("[\n");
            for (int i = 0; i < rows.size(); i++) {
                content.append("  ").append(rows.get(i).toString());
                if (i < rows.size() - 1) {
                    content.append(",");
                }
                content.append("\n");
            }
            content.append("]");

            Files.write(backupFile, content.toString().getBytes(StandardCharsets.UTF_8));
            log.info("Backup saved to: {}", backupFile.toAbsolutePath());
            return null;
        }).then();
    }

    /**
     * Run schema creation and migration scripts
     */
    private Mono<Void> runSchemaAndMigration() {
        return executeSchemaScript()
                .then(executeMigrationScript())
                .doOnSuccess(unused -> log.info("Schema and migration scripts executed successfully"));
    }

    /**
     * Execute schema.sql script
     */
    private Mono<Void> executeSchemaScript() {
        return loadSqlScript("schema.sql")
                .flatMap(this::executeSqlStatements)
                .doOnSuccess(unused -> log.info("Schema script executed successfully"))
                .doOnError(error -> log.error("Schema script execution failed: {}", error.getMessage()));
    }

    /**
     * Execute migration.sql script
     */
    private Mono<Void> executeMigrationScript() {
        return loadSqlScript("migration.sql")
                .flatMap(this::executeSqlStatements)
                .doOnSuccess(unused -> log.info("Migration script executed successfully"))
                .doOnError(error -> log.error("Migration script execution failed: {}", error.getMessage()));
    }

    /**
     * Load SQL script from classpath
     */
    private Mono<String> loadSqlScript(String scriptName) {
        return Mono.fromCallable(() -> {
            try {
                ClassPathResource resource = new ClassPathResource(scriptName);
                return new String(resource.getInputStream().readAllBytes(), StandardCharsets.UTF_8);
            } catch (IOException e) {
                throw new RuntimeException("Failed to load SQL script: " + scriptName, e);
            }
        });
    }

    /**
     * Execute SQL statements from script content
     */
    private Mono<Void> executeSqlStatements(String sqlContent) {
        // Split SQL content by GO statements or semicolons
        List<String> statements = Arrays.stream(sqlContent.split("(?i)\\bGO\\b|;"))
                .map(String::trim)
                .filter(stmt -> !stmt.isEmpty() && !stmt.startsWith("--"))
                .toList();

        return Flux.fromIterable(statements)
                .flatMap(statement -> {
                    log.debug("Executing SQL: {}", statement.substring(0, Math.min(100, statement.length())));
                    return databaseClient.sql(statement)
                            .fetch()
                            .rowsUpdated()
                            .onErrorResume(error -> {
                                // Log error but continue with other statements
                                log.warn("SQL statement failed (continuing): {}", error.getMessage());
                                return Mono.just(0L);
                            });
                })
                .then();
    }

    /**
     * Get backup status and information
     */
    public Mono<String> getBackupStatus() {
        return Mono.fromCallable(() -> {
            Path backupDir = Paths.get(backupPath);
            if (!Files.exists(backupDir)) {
                return "Backup directory does not exist: " + backupPath;
            }

            try {
                long fileCount = Files.list(backupDir).count();
                return String.format("Backup directory: %s, Files: %d", backupPath, fileCount);
            } catch (IOException e) {
                return "Error reading backup directory: " + e.getMessage();
            }
        });
    }
}
