package com.inspeedia.vanning.domain.alert.validator.registry;

import com.inspeedia.vanning.domain.alert.validator.AlertValidator;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.LinkedHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class AlertValidatorRegistry {
    private final Map<String, AlertValidator> validators;

    public AlertValidatorRegistry(List<AlertValidator> validatorList) {
        this.validators = validatorList.stream()
                .collect(Collectors.toMap(
                    validator -> validator.getAlertType().getName(),
                    Function.identity(),
                    (validator1, validator2) -> validator1,
                     LinkedHashMap::new
                ));
    }

    public AlertValidator getValidator(String alertType) {
        return validators.get(alertType);
    }

    public List<AlertValidator> getAllValidators() {
        return List.copyOf(validators.values());
    }

    public List<AlertValidator> getEnabledValidators() {
        // You can add filtering logic here based on configuration
        return getAllValidators();
    }
}
