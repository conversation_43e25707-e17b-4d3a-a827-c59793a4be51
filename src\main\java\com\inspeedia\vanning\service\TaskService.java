package com.inspeedia.vanning.service;

import java.time.LocalDate;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.inspeedia.vanning.domain.ActualWork;
import com.inspeedia.vanning.domain.PlannedWork;
import com.inspeedia.vanning.dto.TaskDto;

import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

/**
 * Service for Task operations
 *
 * This service combines PlannedWork and ActualWork data to provide a unified
 * Task view for the frontend dashboard.
 */
@Service
public class TaskService {

    private final Logger log = LoggerFactory.getLogger(TaskService.class);

    private final PlannedWorkService plannedWorkService;
    private final ActualWorkService actualWorkService;
    private final TaskConverter taskConverter;

    public TaskService(PlannedWorkService plannedWorkService, ActualWorkService actualWorkService, TaskConverter taskConverter) {
        this.plannedWorkService = plannedWorkService;
        this.actualWorkService = actualWorkService;
        this.taskConverter = taskConverter;
    }

    /**
     * Gets all tasks by combining planned and actual work data
     *
     * @return Flux of TaskDto containing combined task information
     */
    public Flux<TaskDto> getAllTasks() {
        log.debug("Fetching all tasks by combining planned and actual work");
        Flux<ActualWork> actualWorkFlux = actualWorkService.getAllActiveActualWork(0, 100);
        Flux<PlannedWork> plannedWorkFlux = plannedWorkService.getAllActivePlannedWork(0, 100);
        return actualWorkFlux
                .groupBy(this::buildKey)
                .flatMap(grouped
                        -> grouped
                        .reduce((a1, a2) -> {
                            if (a1.getStartTime() == null) {
                                return a2;
                            }
                            if (a2.getStartTime() == null) {
                                return a1;
                            }
                            return a1.getStartTime().isAfter(a2.getStartTime()) ? a1 : a2;
                        })
                        .map(latest -> Tuples.of(grouped.key(), latest))
                )
                .collectMap(Tuple2::getT1, Tuple2::getT2)
                .defaultIfEmpty(Map.of())
                .doOnSuccess(map -> log.debug("Actual work map created: {}", map.size()))
                .doOnError(error -> log.error("Error creating actual work map: {}", error.getMessage()))
                .onErrorReturn(Map.of()) // Return empty map if actual work processing fails
                .flatMapMany(actualMap
                        -> plannedWorkFlux.map(planned -> {
                    ActualWork match = actualMap.get(buildKey(planned));
                    try {
                        return taskConverter.convertToTaskDto(planned, match);
                    } catch (Exception e) {
                        log.error("Error mapping planned work to task: {}", e.getMessage());
                        // Return task with planned work only, no actual work data
                        return taskConverter.convertToTaskDto(planned, null);
                    }
                })
                        .onErrorResume(error -> {
                            log.error("Error mapping planned work to task: {}", error.getMessage());
                            return Mono.empty();
                        })
                )
                .doOnComplete(() -> log.debug("Successfully fetched today's tasks for operator"))
                .doOnError(error -> log.error("Error fetching today's tasks for operator: {}", error.getMessage()));
    }

    /**
     * Gets today's tasks by combining planned and actual work data
     *
     * @return Flux of TaskDto containing today's task information
     */
    public Flux<TaskDto> getTodayTasks() {
        log.debug("Fetching all tasks by combining planned and actual work");
        Flux<ActualWork> actualWorkFlux = actualWorkService.getActualWorkForCurrentDate();
        Flux<PlannedWork> plannedWorkFlux = plannedWorkService.getPlannedWorkForToday();
        return actualWorkFlux
                .groupBy(this::buildKey)
                .flatMap(grouped
                        -> grouped
                        .reduce((a1, a2) -> {
                            if (a1.getStartTime() == null) {
                                return a2;
                            }
                            if (a2.getStartTime() == null) {
                                return a1;
                            }
                            return a1.getStartTime().isAfter(a2.getStartTime()) ? a1 : a2;
                        })
                        .map(latest -> Tuples.of(grouped.key(), latest))
                )
                .collectMap(Tuple2::getT1, Tuple2::getT2)
                .defaultIfEmpty(Map.of())
                .doOnSuccess(map -> log.debug("Actual work map created: {}", map.size()))
                .doOnError(error -> log.error("Error creating actual work map: {}", error.getMessage()))
                .onErrorReturn(Map.of()) // Return empty map if actual work processing fails
                .flatMapMany(actualMap
                        -> plannedWorkFlux.map(planned -> {
                    ActualWork match = actualMap.get(buildKey(planned));
                    try {
                        return taskConverter.convertToTaskDto(planned, match);
                    } catch (Exception e) {
                        log.error("Error mapping planned work to task: {}", e.getMessage());
                        // Return task with planned work only, no actual work data
                        return taskConverter.convertToTaskDto(planned, null);
                    }
                })
                        .onErrorResume(error -> {
                            log.error("Error mapping planned work to task: {}", error.getMessage());
                            return Mono.empty();
                        })
                )
                .doOnComplete(() -> log.debug("Successfully fetched today's tasks for operator"))
                .doOnError(error -> log.error("Error fetching today's tasks for operator: {}", error.getMessage()));
    }

    /**
     * Gets today's tasks for a specific operator
     *
     * @param operatorName the name of the operator to filter tasks for
     * @return Flux of TaskDto containing today's tasks for the specified
     * operator
     */
    public Flux<TaskDto> getTodayTasksForOperator(String operatorName) {
        log.debug("Fetching today's tasks the operator: {}", operatorName);

        // Get planned work and actual work in parallel
        Flux<PlannedWork> plannedWorkFlux = plannedWorkService.getPlannedWorkForOperatorFromToday(operatorName);
        Flux<ActualWork> actualWorkFlux = actualWorkService.getActualWorkByUserNameAndWorkDate(operatorName, LocalDate.now());

        // Collect actual work into a map first, with error handling to ensure planned work is always returned
        return actualWorkFlux
                .groupBy(this::buildKey)
                .flatMap(grouped
                        -> grouped
                        .reduce((a1, a2) -> {
                            if (a1.getStartTime() == null) {
                                return a2;
                            }
                            if (a2.getStartTime() == null) {
                                return a1;
                            }
                            return a1.getStartTime().isAfter(a2.getStartTime()) ? a1 : a2;
                        })
                        .map(latest -> Tuples.of(grouped.key(), latest))
                )
                .collectMap(Tuple2::getT1, Tuple2::getT2)
                .defaultIfEmpty(Map.of())
                .doOnSuccess(map -> log.debug("Actual work map created: {}", map.size()))
                .doOnError(error -> log.error("Error creating actual work map: {}", error.getMessage()))
                .onErrorReturn(Map.of()) // Return empty map if actual work processing fails
                .flatMapMany(actualMap
                -> plannedWorkFlux.map(planned -> {
                    ActualWork match = actualMap.get(buildKey(planned));
                    try {
                        return taskConverter.convertToTaskDto(planned, match);
                    } catch (Exception e) {
                        log.error("Error mapping planned work to task: {}", e.getMessage());
                        // Return task with planned work only, no actual work data
                        return taskConverter.convertToTaskDto(planned, null);
                    }
                })
                        .onErrorResume(error -> {
                            log.error("Error mapping planned work to task: {}", error.getMessage());
                            return Mono.empty();
                        })
                ).doOnComplete(() -> log.debug("Successfully fetched today's tasks for operator: {}", operatorName))
                .doOnError(error -> log.error("Error fetching today's tasks for operator: {}", error.getMessage()));
    }

    /**
     * Builds a composite key for matching PlannedWork with ActualWork
     *
     * @param planned the planned work to build key for
     * @return composite key string for matching
     */
    private String buildKey(PlannedWork planned) {
        return planned.getOperatorName() + "|" + planned.getWorkDate() + "|" + planned.getVanGp();
    }

    /**
     * Builds a composite key for matching ActualWork with PlannedWork
     *
     * @param actual the actual work to build key for
     * @return composite key string for matching
     */
    private String buildKey(ActualWork actual) {
        return actual.getOperatorName() + "|" + actual.getWorkDate() + "|" + actual.getVanGp();
    }
}
