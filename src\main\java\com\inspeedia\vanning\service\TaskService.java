package com.inspeedia.vanning.service;

import java.time.Duration;
import java.time.LocalDate;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.inspeedia.vanning.domain.ActualWork;
import com.inspeedia.vanning.domain.PlannedWork;
import com.inspeedia.vanning.dto.TaskDto;

import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;
import reactor.util.retry.Retry;

/**
 * Service for Task operations
 *
 * This service combines PlannedWork and ActualWork data to provide a unified
 * Task view for the frontend dashboard.
 */
@Service
public class TaskService {

    private final Logger log = LoggerFactory.getLogger(TaskService.class);

    private final PlannedWorkService plannedWorkService;
    private final ActualWorkService actualWorkService;
    private final TaskConverter taskConverter;
    private final RequestDeduplicationService requestDeduplicationService;

    public TaskService(PlannedWorkService plannedWorkService, ActualWorkService actualWorkService,
            TaskConverter taskConverter, RequestDeduplicationService requestDeduplicationService) {
        this.plannedWorkService = plannedWorkService;
        this.actualWorkService = actualWorkService;
        this.taskConverter = taskConverter;
        this.requestDeduplicationService = requestDeduplicationService;
    }

    /**
     * Gets all tasks by combining planned and actual work data
     *
     * @return Flux of TaskDto containing combined task information
     */
    public Flux<TaskDto> getAllTasks() {
        log.debug("Fetching all tasks by combining planned and actual work");
        Flux<ActualWork> actualWorkFlux = actualWorkService.getAllActiveActualWork(0, 100);
        Flux<PlannedWork> plannedWorkFlux = plannedWorkService.getAllActivePlannedWork(0, 100);
        return actualWorkFlux
                .groupBy(this::buildKey)
                .flatMap(grouped
                        -> grouped
                        .reduce((a1, a2) -> {
                            if (a1.getStartTime() == null) {
                                return a2;
                            }
                            if (a2.getStartTime() == null) {
                                return a1;
                            }
                            return a1.getStartTime().isAfter(a2.getStartTime()) ? a1 : a2;
                        })
                        .map(latest -> Tuples.of(grouped.key(), latest))
                )
                .collectMap(Tuple2::getT1, Tuple2::getT2)
                .defaultIfEmpty(Map.of())
                .doOnSuccess(map -> log.debug("Actual work map created: {}", map.size()))
                .doOnError(error -> log.error("Error creating actual work map: {}", error.getMessage()))
                .onErrorReturn(Map.of()) // Return empty map if actual work processing fails
                .flatMapMany(actualMap
                        -> plannedWorkFlux.map(planned -> {
                    ActualWork match = actualMap.get(buildKey(planned));
                    try {
                        return taskConverter.convertToTaskDto(planned, match);
                    } catch (Exception e) {
                        log.error("Error mapping planned work to task: {}", e.getMessage());
                        // Return task with planned work only, no actual work data
                        return taskConverter.convertToTaskDto(planned, null);
                    }
                })
                        .onErrorResume(error -> {
                            log.error("Error mapping planned work to task: {}", error.getMessage());
                            return Mono.empty();
                        })
                )
                .doOnComplete(() -> log.debug("Successfully fetched today's tasks for operator"))
                .doOnError(error -> log.error("Error fetching today's tasks for operator: {}", error.getMessage()));
    }

    /**
     * Gets today's tasks by combining planned and actual work data
     *
     * @return Flux of TaskDto containing today's task information
     */
    public Flux<TaskDto> getTodayTasks() {
        log.debug("Fetching all tasks by combining planned and actual work");
        Flux<ActualWork> actualWorkFlux = actualWorkService.getActualWorkForCurrentDate();
        Flux<PlannedWork> plannedWorkFlux = plannedWorkService.getPlannedWorkForToday();
        return actualWorkFlux
                .groupBy(this::buildKey)
                .flatMap(grouped
                        -> grouped
                        .reduce((a1, a2) -> {
                            if (a1.getStartTime() == null) {
                                return a2;
                            }
                            if (a2.getStartTime() == null) {
                                return a1;
                            }
                            return a1.getStartTime().isAfter(a2.getStartTime()) ? a1 : a2;
                        })
                        .map(latest -> Tuples.of(grouped.key(), latest))
                )
                .collectMap(Tuple2::getT1, Tuple2::getT2)
                .defaultIfEmpty(Map.of())
                .doOnSuccess(map -> log.debug("Actual work map created: {}", map.size()))
                .doOnError(error -> log.error("Error creating actual work map: {}", error.getMessage()))
                .onErrorReturn(Map.of()) // Return empty map if actual work processing fails
                .flatMapMany(actualMap
                        -> plannedWorkFlux.map(planned -> {
                    ActualWork match = actualMap.get(buildKey(planned));
                    try {
                        return taskConverter.convertToTaskDto(planned, match);
                    } catch (Exception e) {
                        log.error("Error mapping planned work to task: {}", e.getMessage());
                        // Return task with planned work only, no actual work data
                        return taskConverter.convertToTaskDto(planned, null);
                    }
                })
                        .onErrorResume(error -> {
                            log.error("Error mapping planned work to task: {}", error.getMessage());
                            return Mono.empty();
                        })
                )
                .doOnComplete(() -> log.debug("Successfully fetched today's tasks for operator"))
                .doOnError(error -> log.error("Error fetching today's tasks for operator: {}", error.getMessage()));
    }

    /**
     * Gets today's tasks for a specific operator
     *
     * @param operatorName the name of the operator to filter tasks for
     * @return Flux of TaskDto containing today's tasks for the specified
     * operator
     */
    public Flux<TaskDto> getTodayTasksForOperator(String operatorName) {
        log.debug("Fetching today's tasks for operator: {}", operatorName);

        if (operatorName == null || operatorName.trim().isEmpty()) {
            log.warn("Operator name is null or empty, returning empty flux");
            return Flux.empty();
        }

        // Use request deduplication to handle concurrent requests within 500ms
        String requestKey = "tasks_operator_" + operatorName + "_" + LocalDate.now().toString();
        return requestDeduplicationService.deduplicateFlux(requestKey, () -> fetchTasksForOperator(operatorName));
    }

    /**
     * Internal method to fetch tasks for operator (used by deduplication
     * service)
     */
    private Flux<TaskDto> fetchTasksForOperator(String operatorName) {

        // Get planned work and actual work in parallel with retry logic
        Flux<PlannedWork> plannedWorkFlux = plannedWorkService.getPlannedWorkForOperatorFromToday(operatorName)
                .retryWhen(Retry.backoff(2, Duration.ofMillis(100))
                        .filter(throwable -> !(throwable instanceof IllegalArgumentException))
                        .doBeforeRetry(retrySignal -> log.debug("Retrying planned work fetch for operator: {}, attempt: {}",
                        operatorName, retrySignal.totalRetries() + 1)))
                .onErrorResume(error -> {
                    log.error("Failed to fetch planned work for operator: {} after retries: {}", operatorName, error.getMessage());
                    return Flux.empty();
                });

        Flux<ActualWork> actualWorkFlux = actualWorkService.getActualWorkByUserNameAndWorkDate(operatorName, LocalDate.now())
                .retryWhen(Retry.backoff(2, Duration.ofMillis(100))
                        .filter(throwable -> !(throwable instanceof IllegalArgumentException))
                        .doBeforeRetry(retrySignal -> log.debug("Retrying actual work fetch for operator: {}, attempt: {}",
                        operatorName, retrySignal.totalRetries() + 1)))
                .onErrorResume(error -> {
                    log.warn("Failed to fetch actual work for operator: {} after retries: {}", operatorName, error.getMessage());
                    return Flux.empty();
                });

        // Collect actual work into a map first, with improved error handling
        return actualWorkFlux
                .groupBy(this::buildKey)
                .flatMap(grouped -> grouped
                .reduce((a1, a2) -> {
                    if (a1.getStartTime() == null) {
                        return a2;
                    }
                    if (a2.getStartTime() == null) {
                        return a1;
                    }
                    return a1.getStartTime().isAfter(a2.getStartTime()) ? a1 : a2;
                })
                .map(latest -> Tuples.of(grouped.key(), latest))
                .onErrorResume(error -> {
                    log.warn("Error processing actual work group: {}", error.getMessage());
                    return Mono.empty();
                })
                )
                .collectMap(Tuple2::getT1, Tuple2::getT2)
                .defaultIfEmpty(Map.of())
                .doOnSuccess(map -> log.debug("Actual work map created for operator {}: {} entries", operatorName, map.size()))
                .onErrorReturn(Map.of()) // Return empty map if actual work processing fails
                .flatMapMany(actualMap -> plannedWorkFlux
                .map(planned -> {
                    ActualWork match = actualMap.get(buildKey(planned));
                    try {
                        return taskConverter.convertToTaskDto(planned, match);
                    } catch (Exception e) {
                        log.error("Error converting planned work to task for operator {}: {}", operatorName, e.getMessage());
                        // Return task with planned work only, no actual work data
                        try {
                            return taskConverter.convertToTaskDto(planned, null);
                        } catch (Exception fallbackError) {
                            log.error("Fallback conversion also failed for operator {}: {}", operatorName, fallbackError.getMessage());
                            return null;
                        }
                    }
                })
                .filter(task -> task != null) // Filter out null tasks from failed conversions
                .onErrorContinue((error, item) -> {
                    log.error("Error in task conversion pipeline for operator {}: {}", operatorName, error.getMessage());
                })
                )
                .doOnComplete(() -> log.debug("Successfully fetched today's tasks for operator: {}", operatorName))
                .doOnError(error -> log.error("Error fetching today's tasks for operator {}: {}", operatorName, error.getMessage()))
                .onErrorResume(error -> {
                    log.error("Final error handler for operator {}: {}", operatorName, error.getMessage());
                    return Flux.empty();
                });
    }

    /**
     * Builds a composite key for matching PlannedWork with ActualWork
     *
     * @param planned the planned work to build key for
     * @return composite key string for matching
     */
    private String buildKey(PlannedWork planned) {
        return planned.getOperatorName() + "|" + planned.getWorkDate() + "|" + planned.getVanGp();
    }

    /**
     * Builds a composite key for matching ActualWork with PlannedWork
     *
     * @param actual the actual work to build key for
     * @return composite key string for matching
     */
    private String buildKey(ActualWork actual) {
        return actual.getOperatorName() + "|" + actual.getWorkDate() + "|" + actual.getVanGp();
    }
}
